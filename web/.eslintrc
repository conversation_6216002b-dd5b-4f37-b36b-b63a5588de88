{
  "env": {
    "browser": true,
    "es6": true
  },
  "extends": [
    "plugin:react/recommended",
    "airbnb",
    "airbnb/hooks",
    "plugin:import/errors",
    "plugin:import/warnings",
    "plugin:import/typescript",
    "plugin:@typescript-eslint/eslint-recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:storybook/recommended"
  ],
  "globals": {
    "Atomics": "readonly",
    "SharedArrayBuffer": "readonly"
  },
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": 2018,
    "sourceType": "module"
  },
  "plugins": [
    "react",
    "@typescript-eslint",
    "unicorn",
    "filenames",
    "jsdoc"
  ],
  "ignorePatterns": [
    "/external/**"
  ],
  "rules": {
    "prefer-arrow-callback": 0,
    "class-methods-use-this": "off",
    "linebreak-style": "off",
    "no-use-before-define": "off",
    "padded-blocks": [
      "error",
      {
        "classes": "always"
      }
    ],
    "import/extensions": [
      "error",
      "ignorePackages",
      {
        "js": "never",
        "jsx": "never",
        "ts": "never",
        "tsx": "never"
      }
    ],
    "import/no-extraneous-dependencies": [
      "error",
      {
        "devDependencies": [
          "**/*.test.tsx"
        ]
      }
    ],
    "react/jsx-filename-extension": [
      "error",
      {
        "extensions": [
          ".js",
          ".jsx",
          ".ts",
          ".tsx"
        ]
      }
    ],
    "@typescript-eslint/no-use-before-define": "error",
    "@typescript-eslint/no-unused-vars": [
      "error",
      {
        "argsIgnorePattern": "^_"
      }
    ],
    "function-paren-newline": [
      "error",
      "consistent"
    ],
    "react/function-component-definition": [
      2,
      {
        "namedComponents": "arrow-function",
        "unnamedComponents": "arrow-function"
      }
    ],
    /* --- filenames: TSXのdefault export名とファイル名の整合（Pascal）--- */
    /* storiesは除外（下のoverridesでoff） */
    "filenames/match-exported": "off",
    // function.tsxなどを除外
    "filenames/no-index": "off",
    /* --- jsdoc: TypeScriptと共存しやすい基本チェックのみ --- */
    "jsdoc/check-alignment": "warn",
    "jsdoc/check-tag-names": "warn",
    "jsdoc/no-undefined-types": "warn",
    "jsdoc/require-param-type": "off",
    "jsdoc/require-returns-type": "off"
  },
  "overrides": [
    {
      "files": [
        "**/*.tsx"
      ],
      "rules": {
        /* .tsx は default export 名とファイル名の整合を Pascal に限定して緩くチェック */
        "filenames/match-exported": [
          "warn",
          "pascal"
        ]
      }
    },
    {
      "files": [
        "**/*.{css,scss}"
      ],
      "rules": {
        /* .css / .scss は kebab-case を強制 */
        "unicorn/filename-case": [
          "error",
          {
            "cases": {
              "kebabCase": true
            }
          }
        ]
      }
    },
    {
      /* Storybook / テストは既存どおり緩和 */
      "files": [
        "**/*.stories.*",
        "**/*.test.*"
      ],
      "rules": {
        "react/jsx-props-no-spreading": "off",
        "import/no-extraneous-dependencies": "off",
        "import/no-anonymous-default-export": "off",
        /* storiesは default export がオブジェクトのため整合チェックは無効化 */
        "filenames/match-exported": "off"
      }
    }
  ]
}
