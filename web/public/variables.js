window.Geranium = {
  // routing prefix
  routePrefix: '/001/',
  // Instrumentation key of Application Insights
  connectionString: 'InstrumentationKey=a7736d1b-4449-4690-957b-f17ef02a01b4;IngestionEndpoint=https://japaneast-1.in.applicationinsights.azure.com/;LiveEndpoint=https://japaneast.livediagnostics.monitor.azure.com/',
  // HostName of the SPO site
  spoHostName: 'ydxxq.sharepoint.com',
  // company name shown above the atTane
  companyName: '三菱地所',
  // More information about the application
  appInfoMessages: {
    overview: [
      '本アプリ(atTane)は、三菱地所の様々な場所にある情報を横断的に検索することができ、探したいものがすぐに見つかるアプリです。',
      '検索キーワードを確実に含むアイテムを探したい場合はDefaultタブ、ニュアンスを含めた関連性の高いアイテムを探したい場合はAISearchタブをご利用ください。',
    ],
    searchCategory: [
      '・SPOメニューに掲載されているすべてのSPOサイト',
      '・Outlookに存在する自分が受信、送信したメール、下書き',
      '・Teams上の個人/グループチャットと、参加しているチーム内での会話',
    ],
    searchableItems: [
      '・SPO：タイトル、分類、本文が検索対象となります。添付ファイルは検索対象外となります。',
      '・メール：件名、送受信者、本文、添付ファイルのタイトルが検索対象となります。',
      '　PDF,Excel,Word,PowerPoint,テキストファイルについては中身の文字も対象となります。',
      '　迷惑メールフォルダと削除済みフォルダ内のアイテムは検索対象外となります。',
      '・Teams：メンション、送受信者、スレッドタイトル、本文、添付ファイルのタイトルが検索対象となります。',
    ],
    searchCriteria: [
      '・キーワードが複数入力されている場合、すべてのキーワードを含めた結果が表示されます。',
      '　例１：三菱地所 人事 →  ”三菱地所”と”人事”の両方を含む記事が検索されます。',
      '・いずれかのキーワードを含む検索を行いたい場合、キーワードの間に”OR”を含むと検索できます。',
      '　例２：MEC OR 三菱地所 → ”MEC”または”三菱地所”のどちらかを含む記事が検索されます。',
      '・検索には”()”と”AND”と”OR”を併用できます。優先度は”()” > ”AND” > ”OR”の順番です。',
      '　例３：MEC OR 三菱地所 AND 人事 → ”MEC”を含むか、”三菱地所”と”人事”両方を含む記事が検索されます。',
      '　例４：(MEC OR 三菱地所) AND 人事 → ”MEC”または”三菱地所”を含み、かつ”人事”を含む記事が検索されます。',
      '　',
      '【ご利用上の注意】',
      '・メールの添付ファイルはPC版TeamsのatTaneからのみダウンロードできます。iphone版Teamsの場合はOutlookに遷移して確認をお願いします。',
      '・チャットに利用したループコンポーネントや承認カードなどの特殊な形式はatTaneで表示されないことがあります。チーム・チャットに遷移してご確認をお願いします。',
    ],
    sortOrder: [
      '・ 検索結果は、アプリケーションがアイテムを見つけた順番に表示されるためランダムです。',
      '・ 新しいアイテムが見たい場合は検索バーの下にあるソート機能を使って並べ替えができます。',
      '・ アイテム内でキーワードと一致している箇所がハイライトで表示されます。',
    ],
    contactUs: [],
  },
  // More information about the AI version
  appAIInfoMessages: {
    overview: [
      '本アプリ(atTane)は、三菱地所の様々な場所にある情報を横断的に検索することができ、探したいものがすぐに見つかるアプリです。',
      '検索キーワードを確実に含むアイテムを探したい場合はDefaultタブ、ニュアンスを含めた関連性の高いアイテムを探したい場合はAISearchタブをご利用ください。',
    ],
    searchCategory: [
      '・Outlookに存在する自分が受信、送信したメール、下書き',
    ],
    searchableItems: [
      '・件名、本文、送信者',
      '・Teams：データ取得は1時間ごと（毎時0分）に実行されるため、チャット選択から検索可能まで最大1時間かかります。',
      '　データ量によってアップロード時間が異なるため検索可能になる時間を確約できません。',
      '　毎時0分に検索可能になるわけではありません。',
    ],
    searchCriteria: [
      '・自然言語で検索することができます。',
      '　例１：Aプロジェクトの打合せ日が知りたい。',
      ' →  ”Aプロジェクト”や”打合せ日”に関連するアイテムが上位にヒットします。',
      '・キーワードが完全一致していなくても、関連度の高いアイテムを検索することができます。',
      '　例２：B社の請求書が見たい。',
      ' → ”請求書”以外にも”請求明細書”など類似単語を含むアイテムが上位にヒットします。',
      '・単語での検索も可能です。',
      '　例３：請求書',
      ' → ”請求書”の類似単語を含むアイテムが上位にヒットします。',
      '　',
      '【ご利用上の注意】',
      '・検索結果はスコア上位50件のみ表示されます。',
      '・メールの添付ファイルはPC版TeamsのatTaneからのみダウンロードできます。iphone版Teamsの場合はOutlookに遷移して確認をお願いします。',
      '・チャットに利用したループコンポーネントや承認カードなどの特殊な形式はatTaneで表示されないことがあります。チーム・チャットに遷移してご確認をお願いします。',
    ],
    sortOrder: [
      '・ 検索文言と関連度の高いアイテム順（スコア順）に並んでいます',
    ],
    contactUs: [],
  },
  // バッチリクエストのリトライ回数上限
  defaultRetryCounts: -1,
  // バッチリクエストの既定の待ち時間
  retryRegulationTime: 5000,
  // 検索インターバルの制限時間
  retriableTime: -1,
  // バッチリクエストのリトライ間隔
  additionalWaitingMsec: 1500,
  // 一度に投げるバッチリクエストの塊の数
  batchRequestChunkSize: 20,
  // お気に入り一覧表示時の待ち時間
  bookmarkListWaitingTime: 200,
  // 社則検索アプリURL
  companyRegulationSiteUrl: 'https://www.mec.co.jp/service/',
  // 機能の有効無効で使用する定数
  // company: 'test',
  company: 'mec',
  // AI検索使用の許可
  aiSearchEnabled: true,
  // Teams設定モーダル文言
  searchRestriction: 'チャット選択後、検索可能になるまで最大1時間程度お待ちください。',
};
