{"name": "web", "version": "1.0.0", "private": true, "engines": {"node": "20.x"}, "overrides": {"glob-parent": "^6.0.1"}, "scripts": {"start": "vite --mode localdev --base=./", "build": "vite build --mode develop --base=./", "build:infra": "vite build --mode infra --base=./", "build:mecdev": "vite build --mode mecdev --base=./", "build:stg": "vite build --mode staging --base=./", "build:prod": "vite build --mode prd --base=./", "test": "jest --maxWorkers=50%", "test:ci": "jest --ci --coverage --reporters=default --reporters=jest-junit --coverageReporters=cobertura", "type": "tsc --noEmit", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lintfix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "wlint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "wlintfix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "stylelint": "stylelint --fix --aei 'src/**/*.scss'", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"moduleNameMapper": {"\\.(jpg|ico|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "<rootDir>/src/mocks/file.js", "\\.(css|scss)$": "<rootDir>/src/mocks/style.js", "@microsoft/applicationinsights-react-js": "@avanade-teams/app-insights-reporter/dist/mocks/appInsights", "MgtGetMemo": "<rootDir>/src/mocks/MgtGetMemo"}, "reporters": ["default", "jest-junit"], "transform": {"\\.[jt]sx?$": "@swc/jest"}, "testMatch": ["**/*.test.ts", "**/*.test.tsx"], "testEnvironment": "jsdom", "restoreMocks": true, "transformIgnorePatterns": ["/node_modules/(?!@microsoft/(mgt|microsoft-graph|fast-)|lodash-es|exenv-es6|wc-react|lit-|@fluentui/web-components|@fluentui/react-icons-mdl2|@fluentui/react-icons-mdl2-branded)"], "globalSetup": "./jest-global-setup.js", "setupFiles": ["dotenv/config"]}, "dependencies": {"@avanade-teams/app-insights-reporter": "0.3.1", "@avanade-teams/auth": "0.1.19", "@avanade-teams/deeplink": "0.1.2", "@avanade-teams/teams-info": "0.1.22", "@fluentui/react": "8.122.11", "@fluentui/react-icons-mdl2": "1.3.83", "@fluentui/react-icons-mdl2-branded": "1.2.85", "@fluentui/react-icons-northstar": "0.66.5", "@fluentui/react-northstar": "0.66.5", "@fluentui/theme": "2.6.65", "@microsoft/applicationinsights-common": "3.3.6", "@microsoft/applicationinsights-web": "3.3.6", "@microsoft/microsoft-graph-client": "3.0.7", "@microsoft/microsoft-graph-types": "2.40.0", "@microsoft/teams-js": "1.13.1", "@originjs/vite-plugin-commonjs": "1.0.3", "@vitejs/plugin-react": "4.3.4", "camljs": "2.13.0", "dayjs": "1.11.13", "html-react-parser": "5.2.2", "idb": "8.0.2", "jwt-decode": "3.1.2", "mark.js": "8.11.1", "openai": "^4.72.0", "prop-types": "15.8.1", "react": "^17.0.2", "react-custom-scrollbars-2": "4.5.0", "react-dom": "^17.0.2", "react-innertext": "1.1.5", "react-router-dom": "5.3.0", "react-show-more-text": "1.7.1", "react-use": "17.6.0", "sanitize-html": "2.14.0", "smoothscroll-polyfill": "0.4.4", "storybook-builder-vite": "0.1.23", "tslib": "2.8.1", "vite": "4.4.3", "web-vitals": "4.2.3"}, "devDependencies": {"@babel/core": "7.26.9", "@babel/preset-env": "7.26.9", "@storybook/addon-essentials": "7.6.20", "@storybook/addon-interactions": "7.6.20", "@storybook/addon-links": "7.6.20", "@storybook/react": "7.6.20", "@storybook/react-vite": "7.6.20", "@swc/core": "1.7.26", "@swc/jest": "0.2.17", "@testing-library/jest-dom": "5.16.0", "@testing-library/react": "12.1.2", "@testing-library/react-hooks": "8.0.1", "@types/mark.js": "8.11.12", "@types/microsoft-graph": "2.40.0", "@types/node": "22.13.9", "@types/react": "17.0.37", "@types/react-dom": "17.0.11", "@types/react-router-dom": "5.3.3", "@types/react-show-more-text": "1.4.5", "@types/sanitize-html": "2.13.0", "@types/smoothscroll-polyfill": "0.3.4", "@typescript-eslint/eslint-plugin": "5.5.0", "@typescript-eslint/parser": "5.5.0", "babel-loader": "10.0.0", "eslint": "8.57.0", "eslint-config-airbnb": "19.0.4", "eslint-plugin-filenames": "1.3.2", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsdoc": "50.8.0", "eslint-plugin-jsx-a11y": "6.10.0", "eslint-plugin-react": "7.31.1", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-storybook": "0.12.0", "eslint-plugin-unicorn": "55.0.0", "jest": "27.4.3", "jest-environment-jsdom": "27.4.3", "jest-junit": "13.0.0", "sass": "1.85.1", "storybook": "7.6.20", "storybook-dark-mode": "3.0.3", "stylelint": "16.15.0", "stylelint-config-recommended": "15.0.0", "stylelint-config-recommended-scss": "14.1.0", "ts_dependency_graph": "2.1.1", "ts-jest": "27.1.0", "typescript": "4.9.5"}, "volta": {"node": "20.18.1", "npm": "10.8.2"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}