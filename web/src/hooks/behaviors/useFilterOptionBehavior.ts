import type { ISplitViewState, SplitViewDispatch } from '../../components/domains/split-view/split-view-container/reducers/SplitViewReducer';
import { FilterOption } from '../../types/IContext';

export type UseFilterOptionBehaviorReturn = { onSelectOption: (option: FilterOption) => void }

const useFilterOptionBehavior = (state: ISplitViewState, dispatch: SplitViewDispatch) => {

  function onSelectOption(option: FilterOption) {
    const newFilterOptions = [
      ...(state.context.filter?.filter(({ key }) => key !== option.key) ?? []),
      option,
    ];
    dispatch(
      {
        type: 'SET_FILTER',
        payload: { filter: newFilterOptions },
      },
    );
  }
  return { onSelectOption };
};

export default useFilterOptionBehavior;
