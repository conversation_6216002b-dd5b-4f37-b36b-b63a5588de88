import React from 'react';
import { SplitViewDispatch, SplitViewReducerReturn } from '../../components/domains/split-view/split-view-container/reducers/SplitViewReducer';
import { SplitViewListView } from '../../components/domains/split-view/split-view-list/SplitViewList';
import { ISortOrder } from '../../types/IContext';
import { sortArrayByKey } from '../../utilities/array';
import { isPC } from '../../utilities/mediaQuery';
import { ISplitViewListSingle } from '../../components/domains/split-view/types/ISplitViewListSingle';

type UseBookmarkSwitchButtonBehaviorReturn = [
  onClickBookmarkSwitchButton: () => Promise<void>,
];

/**
 * お気に入り一覧遷移アイコンをクリックしたときのコールバック
 */
export async function onClickBookmarkSwitchButtonImpl(
  bookmarkItems: ISplitViewListSingle[],
  dispatch: SplitViewDispatch,
): Promise<void> {
  // 並び替えられた一覧データ
  const sorted = sortArrayByKey(bookmarkItems, 'reposCreatedDate', 'desc');

  dispatch({
    type: 'SET_DATA',
    payload: {
      list: sorted,
      listView: SplitViewListView.DEFAULT,
      activeId: isPC() ? sorted[0].id ?? '' : '',
    },
  });
}

/**
 * お気に入り遷移アイコンクリック時の振る舞いを制御
 */
const useBookmarkSwitchButtonBehavior = (
  bookmarkItems: ISplitViewListSingle[],
  setBookmarkSortContexts: React.Dispatch<React.SetStateAction<ISortOrder[] | undefined>>,
  useReducerReturn: SplitViewReducerReturn,
): UseBookmarkSwitchButtonBehaviorReturn => {

  const [, dispatch] = useReducerReturn;

  // お気に入り遷移アイコンクリック時のアクション
  const onClickBookmarkSwitchButton = React.useCallback(
    async () => {
      onClickBookmarkSwitchButtonImpl(
        bookmarkItems,
        dispatch,
      );
      setBookmarkSortContexts([]);
    },
    [
      bookmarkItems,
      setBookmarkSortContexts,
      dispatch,
    ],
  );

  return [onClickBookmarkSwitchButton];
};

export default useBookmarkSwitchButtonBehavior;
