import { EventReporter, EventReportType } from '@avanade-teams/app-insights-reporter';
import React from 'react';
import { SplitViewDispatch, SplitViewReducerReturn } from '../../components/domains/split-view/split-view-container/reducers/SplitViewReducer';

import { IContext, ISortOrder } from '../../types/IContext';
import useComponentInitUtility from '../utilities/useComponentInitUtility';
import { SearchModeType } from '../../components/domains/split-view/types/SearchListMode';

type UseSortButtonBehaviorReturn = [
  onClickResultsSortButton: () => Promise<void>,
  onClickBookmarksSortButton: () => Promise<void>,
  onSwitchBookmarksList: () => void,
];

/**
 * 検索結果並び替えボタンクリック時のコールバック
 */
export async function onClickResultsSortButtonImpl(
  context: IContext,
  dispatch: SplitViewDispatch,
  reportEvent: EventReporter,
  searchMode: SearchModeType,
): Promise<void> {
  // モードによってソートキーを決定
  const sortKey = searchMode === 'Chat' ? 'score' : 'displayDate';
  const sorts = context.sort ?? [];
  const current = sorts.find((sort) => sort.key === sortKey);
  // 更新対象の並び替え状況を作成
  const entry: ISortOrder = current
    ? {
      key: current.key,
      order: current.order === 'desc' ? 'asc' : 'desc',
      priority: 1,
    }
    : {
      key: sortKey,
      order: 'desc',
      priority: 1,
    };
  // 元データの中にキー一致するものがあれば削除
  const rest = sorts.filter((sort) => sort.key !== sortKey);
  const toBe = [...(rest || []), entry];

  reportEvent({
    type: EventReportType.USER_EVENT,
    name: searchMode === 'Chat' ? 'EXECUTE_CHAT_SEARCH_LIST_SORT' : 'EXECUTE_DEFAULT_SEARCH_LIST_SORT',
  });

  dispatch({
    type: 'SET_SORT',
    payload: {
      sort: toBe,
    },
  });
}

/**
 * お気に入り一覧並び替えボタンクリック時のコールバック
 */
export async function onClickBookmarksSortButtonImpl(
  context: IContext,
  dispatch: SplitViewDispatch,
  reportEvent: EventReporter,
): Promise<void> {
  const sorts = context.sort ?? [];
  const current = sorts.find((sort) => sort.key === 'displayDate');
  // 更新対象の並び替え状況を作成
  const entry: ISortOrder = current
    ? {
      key: current.key,
      order: current.order === 'desc' ? 'asc' : 'desc',
      priority: 1,
    }
    : {
      key: 'displayDate',
      order: 'desc',
      priority: 1,
    };
  // 元データの中にキー一致するものがあれば削除
  const rest = sorts.filter((sort) => sort.key !== 'displayDate');
  const toBe = [...(rest || []), entry];

  reportEvent({
    type: EventReportType.USER_EVENT,
    name: 'EXECUTE_BOOKMARK_LIST_SORT',
  });

  dispatch(({
    type: 'SET_SORT',
    payload: {
      sort: toBe,
    },
  }));
}

/**
 * お気に入り一覧遷移時にフィルターとソートの設定をリセット
 */
export function onSwitchBookmarksListImpl(
  dispatch: SplitViewDispatch,
): void {

  // フィルターをクリア
  dispatch({
    type: 'SET_FILTER',
    payload: {
      filter: [],
    },
  });

  // ソートをクリア
  dispatch({
    type: 'SET_SORT',
    payload: {
      sort: [],
    },
  });
}

/**
 * 並び替えボタンクリック時の振る舞いを制御
 */
const useSortButtonBehavior = (
  searchMode: SearchModeType,
  useComponentInitReturn: ReturnType<typeof useComponentInitUtility>,
  useSearchResultsReducerReturn: SplitViewReducerReturn,
  useBookmarksReducerReturn: SplitViewReducerReturn,
): UseSortButtonBehaviorReturn => {

  // pick an event reporter from the utility hook
  const [, [reportEvent]] = useComponentInitReturn;

  const [searchResultsState, dispatchSearchResults] = useSearchResultsReducerReturn;
  const [bookmarksState, dispatchBookmarks] = useBookmarksReducerReturn;

  // 検索結果並び替えボタンクリック時のアクション
  const onClickResultsSortButton = React.useCallback(
    async () => onClickResultsSortButtonImpl(
      searchResultsState.context,
      dispatchSearchResults,
      reportEvent,
      searchMode,
    ),
    [
      searchResultsState.context,
      dispatchSearchResults,
      reportEvent,
      searchMode,
    ],
  );

  // お気に入り一覧並び替えボタンクリック時のアクション
  const onClickBookmarksSortButton = React.useCallback(
    async () => onClickBookmarksSortButtonImpl(
      bookmarksState.context,
      dispatchBookmarks,
      reportEvent,
    ),
    [
      bookmarksState.context,
      dispatchBookmarks,
      reportEvent,
    ],
  );

  // お気に入り一覧遷移時のアクション
  const onSwitchBookmarksList = React.useCallback(() => {
    onSwitchBookmarksListImpl(dispatchBookmarks);
  }, [dispatchBookmarks]);

  return [
    onClickResultsSortButton,
    onClickBookmarksSortButton,
    onSwitchBookmarksList,
  ];
};

export default useSortButtonBehavior;
