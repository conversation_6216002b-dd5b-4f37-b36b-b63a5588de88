/* eslint-disable max-len */
import dayjs, { Dayjs, ManipulateType } from 'dayjs';
import type { ISplitViewState } from '../../components/domains/split-view/split-view-container/reducers/SplitViewReducer';
import type { ISplitViewListSingle } from '../../components/domains/split-view/types/ISplitViewListSingle';
import type {
  IContext, FilterOption, IDisplayDateFilterOption,
} from '../../types/IContext';
import { sortArrayByKey } from '../array';

export const MaxFilterDisplayCount = 1000;

export type FilterLeftOptions = {
  displayDate: number[],
  kind: number[],
}

export const OptionValues: {
  displayDate: { option: number, span: [number, string] | 'range' | undefined }[],
  kind: { option: string[] }[]
} = {
  displayDate: [
    { option: 0, span: undefined },
    { option: 1, span: [-1, 'day'] },
    { option: 2, span: [-1, 'week'] },
    { option: 3, span: [-1, 'month'] },
    { option: 4, span: [-6, 'month'] },
    { option: 5, span: [-1, 'year'] },
    { option: 6, span: 'range' },
  ],
  kind: [
    { option: ['SPO'] },
    { option: ['Mail'] },
    { option: ['Chat'] },
  ],
};

const timeCaches: {from: {[key: string]: Dayjs}, to: {[key: string]: Dayjs} } = {
  from: {},
  to: {},
};
function getFrom(from?: string) {
  if (!from) return undefined;
  if (!timeCaches.from[from]) {
    timeCaches.from[from] = dayjs(from).tz('Asia/Tokyo').startOf('day').add(-1, 'ms');
  }
  return timeCaches.from[from];
}
function getTo(to?: string) {
  if (!to) return undefined;
  if (!timeCaches.to[to]) {
    timeCaches.to[to] = dayjs(to).tz('Asia/Tokyo').add(1, 'day').startOf('day');
  }
  return timeCaches.to[to];
}

/**
 * 指定されたアイテムが与えられたフィルター条件を満たすかどうかを判定する。
 * この関数は実際にフィルターをかける際にもカウントする際にも使用される
 *
 * displayDate フィルターの場合:
 * - span が undefined のとき: 常に true（フィルター未指定）
 * - span が 'range' のとき: filter.from／filter.to で指定された日付範囲に item.displayDate が含まれるか
 * - span が [number, unit] のとき: 現在時刻から遡った期間内か（例: [-1, 'day'] → 過去24時間以内）
 *
 * kind フィルターの場合:
 * - filter.option 配列に item.kind が含まれているか
 *
 * @param {ISplitViewListSingle} item - フィルタ対象のリストアイテム
 * @param {FilterOption} filter - 適用するフィルター条件。`key` は 'displayDate' または 'kind'、`option` や range 用の from/to を含む
 * @returns {boolean} 条件を満たす場合は true、そうでなければ false
 */
export const filterFunction = (item: ISplitViewListSingle, filter: FilterOption) => {
  switch (filter.key) {
    case 'displayDate': {
      const options = OptionValues.displayDate[filter.option];
      // 範囲を指定しない場合の選択肢
      if (!options.span) return true;
      // 期間を指定した場合の選択肢
      if (options.span === 'range') {
        const { from, to } = filter;
        const fromDateTime = getFrom(from);
        const toDateTime = getTo(to);
        const date = dayjs(item.displayDate);
        return (!fromDateTime || date.isAfter(fromDateTime, 'ms')) && (!toDateTime || date.isBefore(toDateTime));
      }
      // span が [number, unit] の場合（ここに 24時間以内も該当）
      const [value, unit] = options.span; // たとえば value = -1, unit = 'day'
      // 新しい Date に対して「isBefore(now + value, unit) ではない」ものを残す
      return !dayjs(item.displayDate).isBefore(dayjs(new Date()).add(value, unit as ManipulateType));
    }
    case 'kind': {
      return filter.option.length === 0 || filter.option.some((k) => k === item.kind);
    }
    default:
      return true;
  }
};

function getFilteredItems(
  key: string, original: ISplitViewListSingle[], context: IContext,
): ISplitViewListSingle[] {
  if (!context.filter) return original;
  return context.filter.filter(
    (f) => f.key !== key,
  ).reduce(
    // 1つ1つにフィルターを適用している
    (list, option) => list.filter((i) => filterFunction(i, option)),
    original,
  );
}

// 空のフィルターオプション（FilterLeftOptions型）を非同期に生成する関数
export async function getEmptyFilterOptions(
  { list, context }: ISplitViewState,
): Promise<FilterLeftOptions> {
  // getFilteredItemsで"displayDate"に関するフィルタリング済みのアイテム一覧を取得し、
  // 各オプションごとに該当するアイテム数をカウントするPromiseの配列を生成
  const displayDate = Promise.all(
    // 即時実行関数により、getFilteredItemsで得たフィルタリング済み配列（filtered）に対して処理を実施
    ((filtered: ISplitViewListSingle[]) => OptionValues.displayDate.map( // OptionValues.displayDate内の各オプションに対してマッピング
      ({ option }) => new Promise<number>((resolve) => { // 各オプションごとに新しいPromiseを生成して、該当アイテム数を計算
        let count = 0; // 該当するアイテム数をカウントする変数の初期化
        // filtered配列内のすべてのアイテムに対してループを実施
        for (let index = 0; index < filtered.length; index += 1) {
          const item = filtered[index];
          // filterFunctionでアイテムが条件を満たすかチェックし、条件に一致すればcountを増やす
          count = filterFunction(item, { key: 'displayDate', option }) ? count + 1 : count;
          // カウントがMaxFilterDisplayCountを超えた場合、早期にループを抜ける
          if (count > MaxFilterDisplayCount) break;
        }
        // カウント結果をresolveしてPromiseを完了
        resolve(count);
      }),
    )
    )(getFilteredItems('displayDate', list, context)), // 'displayDate'に基づいてフィルタリングされたアイテム一覧を渡す
  );

  const kind = Promise.all(
    ((filtered: ISplitViewListSingle[]) => OptionValues.kind.map(
      ({ option }) => new Promise<number>((resolve) => {
        let count = 0; // 該当アイテム数をカウントする変数の初期化
        // filtered配列内のすべてのアイテムに対してループを実施
        for (let index = 0; index < filtered.length; index += 1) {
          const item = filtered[index];
          // filterFunctionでアイテムがkindフィルターの条件を満たすかチェックし、条件に一致すればcountを増やす
          count = filterFunction(item, { key: 'kind', option }) ? count + 1 : count;
          // カウントがMaxFilterDisplayCountを超えた場合、ループを早期終了
          if (count > MaxFilterDisplayCount) break;
        }
        // 計算結果をresolveしてPromiseを完了
        resolve(count);
      }),
    )
    )(getFilteredItems('kind', list, context)), // 'kind'に基づいてフィルタリングされたアイテム一覧を渡す
  );

  // displayDateとkindのPromiseの両方が解決されるまで待機し、その結果をresults配列に格納
  const results = await Promise.all([displayDate, kind]);

  // 結果の配列から、displayDateとkindそれぞれのフィルターオプションの値を取り出し、
  // FilterLeftOptions型のオブジェクトとして返す
  return {
    displayDate: results[0], // displayDateフィルターの各オプションの該当アイテム数のリスト
    kind: results[1], // kindフィルターの各オプションの該当アイテム数のリスト
  };
}

export function isMatch(item: ISplitViewListSingle, filter: FilterOption) {
  // TODO: 旧バージョン対策。今後同様の変換が他にも必要になったら共通関数化を検討する
  const modified = (filter.key as string) === 'updatedDate' ? { ...filter, key: 'displayDate' } as IDisplayDateFilterOption : filter;
  return filterFunction(item, modified);
}

const MaxListDisplayCount = 200;
// state.context.filter が変わると以下が走りUIが更新される
/**
 * リスト全体をソートおよびフィルタリングして、表示用のデータを整形する関数
 */
export function formatList(state: ISplitViewState) {
  const sorts = state.context.sort ?? [];
  const filters = state.context.filter ?? [];
  const list = state.list ?? [];
  const sortOrders = sorts.sort(
    (a, b) => (a.priority ?? 0) - (b.priority ?? 0),
  );

  let sorted = list;
  for (let index = 0; index < sortOrders.length; index += 1) {
    const sorter = sortOrders[index];
    sorted = sortArrayByKey(sorted,
      // TODO: 旧バージョン対策。今後同様の変換が他にも必要になったら共通関数化を検討する
      ((sorter.key as string) === 'updatedDate' ? 'displayDate' : sorter.key) as keyof ISplitViewListSingle,
      sorter.order);
  }

  const filtered: ISplitViewListSingle[] = [];
  // ソート済みリストからフィルタ条件を適用してデータを抽出
  for (let i = 0; i < sorted.length; i += 1) {
    const item = sorted[i];
    if (filters.length === 0 || filters.every((f) => isMatch(item, f))) {
      filtered.push(item);
    }

    // 最大表示数（MaxListDisplayCount）に達したら処理を終了
    if (filtered.length > MaxListDisplayCount) break;
  }

  // ソート＆フィルタリングされたリストを返す
  return filtered;
}
