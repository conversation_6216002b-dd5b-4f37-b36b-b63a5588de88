import * as React from 'react';
import { Dispatch, SetStateAction } from 'react';
import useComponentInitUtility from '../../../../../hooks/utilities/useComponentInitUtility';
import useSharePointApiAccessor from '../../../../../hooks/accessors/useSharePointApiAccessor';
import useSearchRequestApiAccessor from '../../../../../hooks/accessors/useSearchRequestApiAccessor';
import { isSharedLinkCommand } from '../../../../../utilities/regexp';
import { ListMode, ListModeType } from '../../types/ListMode';
import { SplitViewReducerReturn } from '../reducers/SplitViewReducer';
import {
  onBlurSearchInputImpl,
  onChangeSearchInputImpl,
  onSwitchLastSearchResultImpl,
  onClearSearchInputImpl,
  onSubmitSearchInputImpl,
  onUpdateLastSubmittedWords,
  onIntervalImpl,
  initializeRefs,
  convertMail,
  convertChat,
  convertSPO,
  convertUnknown,
  onSubmitChatSearchImpl,
} from './use-search-result-list/functions';
import { ISearchRequestResult } from '../../../../../types/ISearchRequestResult';
import { ISplitViewListSingle } from '../../types/ISplitViewListSingle';
import { SearchListMode, SearchModeType } from '../../types/SearchListMode';
import useIntervalAfterInitialRunBehavior from '../../../../../hooks/behaviors/useIntervalAfterInitialRunBehavior';
import useSearchResultRepositoryAccessor from '../../../../../hooks/accessors/useSearchResultRepositoryAccessor';
import { SplitViewDetailMessage, SplitViewDetailView } from '../../split-view-detail/SplitViewDetail';
import { SplitViewListMessage, SplitViewListView } from '../../split-view-list/SplitViewList';
import { initialPerformanceMetrics } from '../../../../../types/PerformanceMetrics';
import useGraphApiRequestQueue from '../../../../../hooks/behaviors/useGraphApiRequestQueue';
import useOpenAIApiAccessor from '../../../../../hooks/accessors/useOpenAIApiAccessor';
import useUserApiAccessor from '../../../../../hooks/accessors/useUserApiAccessor';

/**
 * 検索要求APIから結果が1件も返ってきていないときのインターバル取得タイミング(ミリ秒)
 */
const INITIAL_INTERVAL = 1 * 1000;

/**
 * 検索要求処理の管理ref初期値
 */
const initialRequestManager = {
  reqId: '',
  retryCount: 0,
  processes: [],
  hasError: false,
  loaded: false,
  hasIncompleteRequests: false,
} as IRequestManager;

export type UseSearchResultListReturnType = {
  searchInputValue: string,
  lastSubmittedWords: string,
  displaySearchWord: string,
  allListDataRef: React.MutableRefObject<ISplitViewListSingle[]>,
  isLoading: boolean,
  replyStateReturn: [reply: string, setReply: (reply: string) => void],
  onChangeSearchInput: (value: string) => void,
  onBlurSearchInput: () => void,
  onClearSearchInput: () => void,
  onSubmitSearchInput: () => void,
  onSwitchLastSearchResult: () => void,
  onInitializeRefs: (newReqId: string) => void,
  dateFilterRef: React.MutableRefObject<{from?: string, to?: string}>;
  sourceFilterRef: React.MutableRefObject<string>;
};

export interface IProcess {
  pid: string,
  status: 'InProgress' | 'Done',
  /**
   * 検索処理の中でリトライ上限/不可エラーが発生したかどうか
   */
  hasRetryError: boolean,
}
export interface IRequestManager {
  reqId: string,
  /**
   * 検索全体のリトライカウント
   */
  retryCount: number,
  processes: IProcess[],
  hasError: boolean,
  loaded: boolean,
  hasIncompleteRequests: boolean,
}

/**
 *  リクエスト中の多重送信防止
 */
export function restraintToSubmitWhileLoading(
  isLoading: boolean,
  setIsLoading: (value: boolean) => void,
  submit: () => void,
): void {
  if (isLoading) return;
  setIsLoading(true);
  submit();
}
/**
 *listViewを元にLoading状態を決定
 */
export function checkListViewLoadingState(
  listView: string,
  setIsLoading: (value: boolean) => void,
): void {
  switch (listView) {
    case SplitViewListView.DEFAULT:
    case SplitViewListView.SEARCH_COMPLETED:
    case SplitViewListView.ERROR:
      setIsLoading(false);
      break;
    case SplitViewListView.LOADING:
      setIsLoading(true);
      break;
    default:
      break;
  }
}

/**
 * 検索結果一覧の機能
 * @param listModeStateReturn 依存するカスタムフックの戻り値
 * @param searchRequestStateReturn 依存するカスタムフックの戻り値
 * @param useReducerReturn 依存するカスタムフックの戻り値
 * @param useComponentInitReturn 依存するカスタムフックの戻り値
 * @param useSPListReturn 依存するカスタムフックの戻り値
 * @param setSearchWords 検索キーワードを外部のstateに保存するためのsetState関数
 * @param hideHighlight 検索結果のハイライトを解除する関数
 * @param useSearchRequestReturn 依存するカスタムフックの戻り値
 * @param useSearchResultRepositoryReturn 依存するカスタムフックの戻り値
 * @param setSyncBookmark お気に入り同期処理開始のsetAction
 */
const useSearchResultList = (
  listModeStateReturn: [ListModeType, (listMode: ListModeType) => void],
  searchRequestStateReturn: [
    ISearchRequestResult | null, (result: ISearchRequestResult | null) => void
  ],
  // デフォルトタブ用のstate
  useReducerReturn: SplitViewReducerReturn,
  // チャットタブ用のstate
  useChatReducerReturn: SplitViewReducerReturn,
  useComponentInitReturn: ReturnType<typeof useComponentInitUtility>,
  useSPListReturn: ReturnType<typeof useSharePointApiAccessor>,
  useUserApiReturn: ReturnType<typeof useUserApiAccessor>,
  setSearchWords: Dispatch<SetStateAction<string | undefined>>,
  hideHighlight: () => void,
  useSearchRequestReturn: ReturnType<typeof useSearchRequestApiAccessor>,
  useSearchResultRepositoryReturn: ReturnType<typeof useSearchResultRepositoryAccessor>,
  useGraphApiRequestQueueReturn: ReturnType<typeof useGraphApiRequestQueue>,
  setSyncBookmark: (start: boolean) => void,
  cancellationRef: React.MutableRefObject<boolean>,
  searchModeStateReturn: [SearchModeType, (mode: SearchModeType) => void],
  oid: string,
): UseSearchResultListReturnType => {

  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  // listMode
  const [listMode, setListMode] = listModeStateReturn;
  // listModeのcache
  const listModeCache = React.useRef<ListModeType>(listMode);
  // searchMode
  const [searchMode] = searchModeStateReturn;
  // searchModeのcache
  const searchListModeCache = React.useRef<SearchModeType>(searchMode);
  const [interval, setInterval] = React.useState<number | null>(null);
  const [searchRequest, setSearchRequest] = searchRequestStateReturn;
  // 検索要求処理の状況(リトライ回数)を管理するref
  const requestManagerRef = React.useRef<IRequestManager>(initialRequestManager);
  const setRequestManagerRef = React.useCallback((override: Partial<IRequestManager> | null) => {
    requestManagerRef.current = override
      ? {
        ...(requestManagerRef.current ?? {}),
        ...override,
      }
      : initialRequestManager;
  }, []);
  // キャンセルフラグの有無監視
  const cancelRef = cancellationRef;
  const [{ listView, list }, dispatch] = useReducerReturn;
  const [{ listView: chatListView, list: chatlist }, chatDispatch] = useChatReducerReturn;
  // デフォルトタブの検索結果を保持
  const allListDataRef = React.useRef<ISplitViewListSingle[]>(list);
  // チャットモード中に取得した検索結果一覧を保持
  const lastChatResultsRef = React.useRef<ISplitViewListSingle[]>(chatlist);
  // デフォルトタブ検索結果のキャッシュを更新
  const setAllListDataRef = React.useCallback((values: ISplitViewListSingle[]) => {
    if (!cancellationRef.current) {
      allListDataRef.current = values;
      // TODO: functions.tsでSET_LISTしてリストを登録している処理をここに集約させたいが
      // 集約させるとキャッシュから復帰させるときのローディング表示がバグる
      // 現状はリスト更新の効率が良くないのでどこかで手を入れたい
      dispatch({
        type: 'SET_DATA',
        payload: {
          list: allListDataRef.current,
        },
      });
    }
  }, [cancellationRef, dispatch]);
  // チャットタブ検索結果キャッシュの更新
  const setLastChatResultsRef = React.useCallback((values: ISplitViewListSingle[]) => {
    if (!cancellationRef.current) {
      lastChatResultsRef.current = values;
      chatDispatch({
        type: 'SET_DATA',
        payload: {
          list: lastChatResultsRef.current,
        },
      });
    }
  }, [cancellationRef, chatDispatch]);
  const [inputValue, setInputValue] = React.useState('');
  // デフォルトモードの検索クエリ記憶
  const defaultInputCacheRef = React.useRef<string>(inputValue);
  // チャットモードの前回検索結果キャッシュ
  const lastChatResultsCacheRef = React.useRef<ISplitViewListSingle[]>([]);
  // 要約機能state
  const replyStateReturn = React.useState<string>('');
  // 要約更新関数
  const [, setReply] = replyStateReturn;
  // UIに表示されるのはlastSubmittedWords
  const [lastSubmittedWords, setLastSubmittedWords] = React.useState<string>('');
  // 前回検索クエリとして画面表示される
  const [chatLastSubmittedWords, setChatLastSubmittedWords] = React.useState<string>('');
  // lastSubmittedWordsの前回の状態/内部処理で使用
  const lastSubmittedWordsRef = React.useRef<string>('');
  const setLastSubmittedWordsRef = React.useCallback((values: string) => {
    lastSubmittedWordsRef.current = values;
  }, []);
  // 最後に送信(Submit)した検索文字列(分割前)のref
  const lastSubmitterOriginRef = React.useRef<string>('');
  // useComponentInitUtility
  const [
    isUnmounted,
    [reportEvent, reportMetric], , , , , , metrics,
  ] = useComponentInitReturn;
  const [performanceMetrics, setPerformanceMetrics] = metrics;
  const { setSearchRequestCache, setSearchResultsCache } = useSearchResultRepositoryReturn;
  // SharePoint検索機能
  const [, fetchList] = useSPListReturn;
  // ユーザーのGroupIdを取得
  const { fetchGroupId } = useUserApiReturn;
  // 日付フィルターState
  const [, setDateFilter] = React.useState<string>('');
  const dateFilterRef = React.useRef<{ from: string, to:string}>({ from: '', to: '' });
  // 検索対象フィルターState
  const [, setSourceFilter] = React.useState<string>('');
  const sourceFilterRef = React.useRef<string>('');
  // チャットモード検索関数
  const { fetchResultFromAI } = useOpenAIApiAccessor();
  // 入力イベント(onChange)のコールバック
  const onChangeSearchInput = React.useCallback((value: string) => {
    onChangeSearchInputImpl(listMode, listView, value, setInputValue);
  }, [listMode, listView, setInputValue]);

  // クリアイベント(onClear)のコールバック
  const onClearSearchInput = React.useCallback(() => {
    onClearSearchInputImpl(
      listMode,
      setListMode,
      setInputValue,
      setSearchWords,
      setRequestManagerRef,
      setAllListDataRef,
      dispatch,
    );
  }, [
    listMode,
    setListMode,
    setInputValue,
    setSearchWords,
    setRequestManagerRef,
    setAllListDataRef,
    dispatch,
  ]);

  // フォーカスアウトイベント(onBlur)のコールバック
  const onBlurSearchInput = React.useCallback(() => {
    onBlurSearchInputImpl(listMode, inputValue, setInputValue);
  }, [listMode, inputValue]);
  // AISearch検索結果変換
  const convertResultToSplitViewListSingle = React.useCallback(
    (results: any[]): ISplitViewListSingle[] => results.map((result) => {
      const kind = result.document?.kind;
      if (kind === 'Mail') {
        return convertMail(result);
      }
      if (kind === 'Chat') {
        return convertChat(result);
      }
      if (kind === 'SPO') {
        return convertSPO(result);
      }
      return convertUnknown(result);
    }),
    [],
  );

  // 検索イベント送信の実体部分、デフォルトモードの検索機能
  const submitSearchRequest = React.useCallback(() => {
    setPerformanceMetrics({
      ...initialPerformanceMetrics,
      viewDetailsFromCache: {
        ...initialPerformanceMetrics.viewDetailsFromCache,
        isAvailable: false,
      },
    });
    // ハイライト文字の状態を管理
    setSearchWords(inputValue);
    const resetCancelRef = () => {
      cancelRef.current = false;
    };
    // onSubmitSearchInputImplにより各種状態を初期化・更新
    // setSearchRequestCacheとsetSearchResultsCacheにより
    // 検索要求と検索結果が更新され、次回起動時の復元に利用される
    const submittedValue = onSubmitSearchInputImpl(
      setListMode,
      inputValue,
      setLastSubmittedWords,
      setSearchRequest,
      setRequestManagerRef,
      setAllListDataRef,
      setSyncBookmark,
      setSearchRequestCache,
      setSearchResultsCache,
      resetCancelRef,
      dispatch,
    );
    if (submittedValue) {
      lastSubmitterOriginRef.current = submittedValue;
      // 検索要求を送った後に常に前回履歴の初期値が空文字に設定される
      setLastSubmittedWordsRef('');
      // パフォーマンス測定開始
      const performanceNow = performance.now();
      performanceMetrics.current.allSearchCompleted.start = performanceNow;
      performanceMetrics.current.showFirstSearchResults.start = performanceNow;
    }
    if (submittedValue.length > 0) {
      setIsLoading(false);
    }
  }, [
    setListMode,
    inputValue,
    setLastSubmittedWordsRef,
    setSearchRequest,
    setSearchWords,
    setRequestManagerRef,
    setAllListDataRef,
    setSyncBookmark,
    setSearchRequestCache,
    setSearchResultsCache,
    dispatch,
    performanceMetrics,
    setPerformanceMetrics,
    cancelRef,
  ]);
  // チャットモードの検索コールバック関数
  const fetchAIResult = React.useCallback(
    () => onSubmitChatSearchImpl(
      inputValue,
      oid,
      fetchResultFromAI,
      fetchGroupId,
      setListMode,
      setInputValue,
      dateFilterRef.current.from,
      dateFilterRef.current.to,
      setDateFilter,
      sourceFilterRef.current,
      setSourceFilter,
      // 前回検索クエリ保持
      setChatLastSubmittedWords,
      // 前回検索結果保持
      setLastChatResultsRef,
      chatDispatch,
      setReply,
      convertResultToSplitViewListSingle,
      reportEvent,
    ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      inputValue,
      oid,
      fetchResultFromAI,
      setListMode,
      setInputValue,
      setChatLastSubmittedWords,
      setLastChatResultsRef,
      chatDispatch,
      convertResultToSplitViewListSingle,
    ],
  );
  // 送信イベント(onSubmit)のコールバック
  const onSubmitSearchInput = React.useCallback(() => {
    restraintToSubmitWhileLoading(isLoading, setIsLoading, () => {
      if (searchMode === SearchListMode.Chat) {
        fetchAIResult();
        // 検索が完了すればLoading状態を解除
        setIsLoading(false);
      } else {
        // Defaultモードで検索開始
        submitSearchRequest();
      }
    });
  }, [isLoading, searchMode, fetchAIResult, submitSearchRequest]);

  // 前回検索結果への切替コールバック
  const onSwitchLastSearchResult = React.useCallback(() => {
    if (!searchRequest || !searchRequest.condition) return;

    // 前回検索結果表示開始時刻を設定
    performanceMetrics.current.viewDetailsFromCache.start = performance.now();

    const { condition } = searchRequest;

    const submittedValue = onSwitchLastSearchResultImpl(
      setListMode, condition, setLastSubmittedWords,
    );

    // 更新された場合は前回履歴の初期値が空文字に設定
    if (submittedValue) {
      setLastSubmittedWordsRef('');
    }
  }, [
    searchRequest,
    setListMode,
    setLastSubmittedWordsRef,
    performanceMetrics,
  ]);

  // 検索要求に関する各種Ref初期化
  const onInitializeRefs = React.useCallback((newReqId: string) => {
    initializeRefs(newReqId, setAllListDataRef, setRequestManagerRef);
  }, [
    setAllListDataRef,
    setRequestManagerRef,
  ]);

  // 検索要求登録機能
  const { getSearchRequestApi, postSearchRequestApi } = useSearchRequestReturn;
  const { process } = useGraphApiRequestQueueReturn;
  React.useEffect(() => {
    const currentListView = searchMode === SearchListMode.Chat ? chatListView : listView;
    checkListViewLoadingState(currentListView, setIsLoading);
  }, [chatListView, listView, setIsLoading, searchMode]);

  // 検索を一定間隔で実行し、APIからデータを取得
  const onInterval = React.useCallback(async (onSuccess?: () => void) => {
    const onSuccessCallback = () => {
      onSuccess?.();
    };
    onIntervalImpl(
      searchRequest,
      getSearchRequestApi,
      fetchList,
      process,
      dispatch,
      reportEvent,
      reportMetric,
      isUnmounted,
      metrics,
      allListDataRef,
      setAllListDataRef,
      requestManagerRef,
      setRequestManagerRef,
      setSearchRequest,
      setInterval,
      useSearchResultRepositoryReturn,
      cancellationRef,
      onSuccessCallback,
    );
  }, [
    cancellationRef,
    getSearchRequestApi,
    setSearchRequest,
    setAllListDataRef,
    setRequestManagerRef,
    searchRequest,
    process,
    dispatch,
    allListDataRef,
    fetchList,
    reportEvent,
    reportMetric,
    useSearchResultRepositoryReturn,
    isUnmounted,
    metrics,
  ]);

  /**
   * fetch initial content and start interval fetching
   */
  useIntervalAfterInitialRunBehavior(
    interval,
    requestManagerRef.current.processes.length === 0,
    onInterval,
  );
  // 検索要求が存在する場合、初回レンダリングやキャッシュからの復元を行う
  React.useEffect(() => {
    // 既に検索要求があるなら
    if (
      !searchRequest
      || !searchRequest.reqId
      || !searchRequest.condition
      || !searchRequest.conditionKeywords
    ) return;
    // 検索バーに入力された値が存在しない場合は検索要求から取得したconditionを設定する
    if (!lastSubmitterOriginRef.current) {
      // ハイライト文字の状態を管理
      setSearchWords(searchRequest.condition);
      // 前回の検索バー入力値を代入
      setInputValue(searchRequest.condition);
      // 前回の検索文言を代入
      setLastSubmittedWords(searchRequest.condition);

      if (!requestManagerRef.current.loaded) {
        // ローディング表示を開始
        dispatch({
          type: 'SET_DATA',
          payload: {
            list: [],
            listView: SplitViewListView.LOADING,
            listMessage: SplitViewListMessage.BLANK,
            activeId: '',
            detail: undefined,
            detailView: SplitViewDetailView.LOADING,
            detailMessage: SplitViewDetailMessage.BLANK,
          },
        });

        setRequestManagerRef({ loaded: true });
      }
    }
    setInterval(INITIAL_INTERVAL);
  }, [dispatch, searchRequest, setInputValue, setRequestManagerRef, setSearchWords]);

  // 送信イベント(onSubmit)発火後の処理
  React.useEffect(() => {
    if (isLoading) return;
    // 既に検索要求が存在している場合はreturnする、２度押し防止
    if (searchRequest || requestManagerRef.current.reqId) return;
    // 検索モードの時だけ処理する
    if (listModeCache.current !== ListMode.SEARCH) return;
    onUpdateLastSubmittedWords(
      lastSubmittedWords,
      postSearchRequestApi,
      getSearchRequestApi,
      setSearchRequest,
      useSearchResultRepositoryReturn,
      dispatch,
      reportEvent,
      isUnmounted,
      lastSubmittedWordsRef,
      listModeCache,
      setLastSubmittedWordsRef,
      setRequestManagerRef,
      metrics,
    );
  }, [
    isLoading,
    searchRequest,
    lastSubmittedWords,
    postSearchRequestApi,
    getSearchRequestApi,
    setSearchRequest,
    useSearchResultRepositoryReturn,
    setSearchWords,
    dispatch,
    reportEvent,
    isUnmounted,
    setLastSubmittedWordsRef,
    setRequestManagerRef,
    metrics,
  ]);

  /**
   * listModeを監視し、検索モードから初期表示モードに移るときにだけ入力値をクリアする
   * setSearchWords(undefined)も実行し、外部のstateもクリアする
   */
  React.useEffect(() => {
    if (listModeCache.current === listMode) return;
    if (listModeCache.current === ListMode.SEARCH && listMode === ListMode.INITIAL_DISPLAY) {
      hideHighlight();
      setInputValue('');
      setSearchWords(undefined);
    }
    listModeCache.current = listMode;
  }, [listMode, setSearchWords, hideHighlight, setInputValue]);

  // ChatModeとDefaultModeの切り替え時に状態を復元する
  React.useEffect(() => {
    // ChatMode
    if (searchMode === SearchListMode.Chat) {
      // inputValueのキャッシュを保存、デフォルトモードのハイライト対象を保持
      defaultInputCacheRef.current = inputValue;
      // ハイライト解除
      setSearchWords('');
      // 検索クエリをクリア
      setInputValue('');
      // チャット検索結果をキャッシュに反映
      lastChatResultsCacheRef.current = lastChatResultsRef.current;
      setChatLastSubmittedWords(chatLastSubmittedWords);
      // チャット履歴があるかチェック
      if (lastChatResultsCacheRef.current.length > 0) {
        chatDispatch({
          type: 'SET_DATA',
          payload: {
            listView: SplitViewListView.SEARCH_COMPLETED,
            // activeId: lastChatResultsCacheRef.current[0].id,
            activeId: '',
            list: lastChatResultsCacheRef.current,
          },
        });
        // すでにChatModeのハイライトは表示されているため
        // チャットモードでは明示的にハイライトを解除
        hideHighlight();
      } else {
        // チャット履歴がない場合は初期表示
        chatDispatch({
          type: 'SET_DATA',
          payload: {
            listMessage: SplitViewListMessage.INITIAL_DISPLAY,
            detailView: SplitViewDetailView.ERROR,
            // 下を消すとデフォルトでは表示されないものの、chatでは表示される
          },
        });
      }
    // DefaultMode
    } else {
      setInputValue(defaultInputCacheRef.current);
      setSearchWords(defaultInputCacheRef.current);
      setLastSubmittedWords(lastSubmittedWords);
      dispatch({
        type: 'SET_DATA',
        payload: {
          list: allListDataRef.current,
          listMessage: '',
        },
      });
    }
    // キャッシュ用の現在のモードを更新
    searchListModeCache.current = searchMode;
  // 依存配列は searchListMode の変化時のみ実行するようにする
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchMode, setListMode, ListMode]);
  const currentLastSubmittedWords = React.useMemo(() => (searchMode === SearchListMode.Chat
    ? chatLastSubmittedWords
    : lastSubmittedWords), [searchMode, chatLastSubmittedWords, lastSubmittedWords]);

  // 表示用の検索文字列（共有リンクの場合は固定文字列）
  const displaySearchWord = React.useMemo(() => {
    if (isSharedLinkCommand(currentLastSubmittedWords)) {
      return '共有された投稿';
    }
    return currentLastSubmittedWords;
  }, [currentLastSubmittedWords]);

  return {
    searchInputValue: inputValue,
    isLoading,
    lastSubmittedWords: currentLastSubmittedWords,
    displaySearchWord,
    allListDataRef,
    replyStateReturn,
    onChangeSearchInput,
    onBlurSearchInput,
    onClearSearchInput,
    onSubmitSearchInput,
    onSwitchLastSearchResult,
    onInitializeRefs,
    dateFilterRef,
    sourceFilterRef,
  };
};

export default useSearchResultList;
