import * as React from 'react';
import useComponentInitUtility from '../../../../../hooks/utilities/useComponentInitUtility';
import useBookmarkRepositoryAccessor from '../../../../../hooks/accessors/useBookmarkRepositoryAccessor';
import { isPC } from '../../../../../utilities/mediaQuery';
import { fillUpLackedData } from '../../../../../utilities/transform';
import { SplitViewDetailMessage, SplitViewDetailView } from '../../split-view-detail/SplitViewDetail';
import { SplitViewListMessage, SplitViewListView } from '../../split-view-list/SplitViewList';
import { ISplitViewListSingle } from '../../types/ISplitViewListSingle';
import { ListMode, ListModeType } from '../../types/ListMode';
import { ISplitViewState, SplitViewDispatch } from '../reducers/SplitViewReducer';
import { mergeCollectionsByKey } from '../../../../../utilities/array';
import environment from '../../../../../utilities/environment';

/**
 * dispatch items
 * @param items
 * @param dispatch
 */
export function applyBookmarkList(
  items: ISplitViewListSingle[],
  dispatch: SplitViewDispatch,
): Promise<void> {

  // zero-length display
  if (items.length === 0) {
    dispatch({
      type: 'SET_NO_ITEMS',
      payload: {
        listMessage: SplitViewListMessage.NO_BOOKMARKS,
        detailMessage: SplitViewDetailMessage.NO_BOOKMARKS,
      },
    });
    return Promise.resolve();
  }

  const bookmarks = [...items];

  // TODO: 初回起動後初めてお気に入りに遷移したときに何も表示されない問題 (#36986) の暫定対応
  setTimeout(() => {
    dispatch({
      type: isPC() ? 'SET_LIST_WITH_DEFAULT' : 'SET_LIST',
      payload: {
        list: bookmarks,
      },
    });
    dispatch({
      type: 'SET_LISTVIEW',
      payload: {
        listView: SplitViewListView.DEFAULT,
      },
    });
  }, environment.REACT_APP_BOOKMARK_LIST_WAITING_TIME);

  return Promise.resolve();
}

/**
 * listModeの変更を監視して条件に応じてコールバックを実行
 * @param listMode
 * @param listModeCache
 * @param onListModeChanged
 * @param onListModeIsBookmarksMode
 */
export function recreateListOnListModeChange(
  listMode: ListModeType,
  listModeCache: React.MutableRefObject<ListModeType>,
  onListModeChanged: () => void,
  onListModeIsBookmarksMode: () => void,
): void {

  // 前回のlistModeと同じ場合は何もしない
  if (listModeCache.current === listMode) return;

  // 今回のlistLModeをrefに保存するなどの処理を実行
  onListModeChanged();

  // listModeがお気に入りのときは一覧を再生成するなどを実行
  if (listMode === ListMode.BOOKMARKS) {
    onListModeIsBookmarksMode();
  }
}

/**
 * update the state when allBookmarks is changed
 * @param fetchUpdates
 * @param listModeCache
 * @param setAllBookmarksCache
 * @param allBookmarksCache
 * @param allBookmarks
 * @param dispatch
 * @param list
 * @param report
 */
export function onAllBookmarksChange(
  listModeCache: React.MutableRefObject<ListModeType>,
  setAllBookmarksCache: (str: string) => void,
  allBookmarksCache: React.MutableRefObject<string>,
  allBookmarks: ISplitViewListSingle[],
  dispatch: SplitViewDispatch,
  list: ISplitViewListSingle[],
): Promise<void> {
  if (listModeCache.current !== ListMode.BOOKMARKS) return Promise.resolve();

  // check the allBookmarks is changed
  const allBookmarksStr = JSON.stringify(allBookmarks);
  if (allBookmarksStr === allBookmarksCache.current) return Promise.resolve();

  // update the cache
  setAllBookmarksCache(allBookmarksStr);

  const converted = allBookmarks.map((bookmark) => fillUpLackedData(bookmark));
  // allBookmarksが変更されていた時にリスト表示を更新する
  return applyBookmarkList(
    mergeCollectionsByKey('id', list, converted, false),
    dispatch,
  );
}

const useBookmarksList = (
  listMode: ListModeType,
  useReducerReturn: ReturnType<typeof React.useReducer>,
  useComponentInitReturn: ReturnType<typeof useComponentInitUtility>,
  useBookmarkRepositoryReturn: ReturnType<typeof useBookmarkRepositoryAccessor>,
  syncBookmarkStateReturn: [boolean, (start: boolean) => void],
): void => {

  // state
  const [{ list }, dispatch] = useReducerReturn as [ISplitViewState, SplitViewDispatch];

  // 前回のリストモードのキャッシュ
  const listModeCache = React.useRef<ListModeType>(listMode);

  // IndexedDB feature
  const { allBookmarks } = useBookmarkRepositoryReturn;

  // お気に入り同期開始State
  const [syncBookmark, setSyncBookmark] = syncBookmarkStateReturn;

  // event reporter feature
  const [, [report]] = useComponentInitReturn;

  /**
   * お気に入り一覧から生成した記事一覧を、お気に入り一覧遷移時にstateへ反映する
   */
  const onSwitchToBookmarksList = React.useCallback(
    () => {
      dispatch({
        type: 'SET_DATA',
        payload: {
          list: [],
          listView: SplitViewListView.LOADING,
          listMessage: SplitViewListMessage.BLANK,
          activeId: '',
          detail: { id: '', kind: 'Other', title: '' },
          detailView: SplitViewDetailView.LOADING,
          detailMessage: SplitViewDetailMessage.BLANK,
        },
      });

      applyBookmarkList(
        allBookmarks,
        dispatch,
      );
    },
    [
      allBookmarks,
      dispatch,
    ],
  );

  /**
   * listModeの変更を監視して条件に応じてコールバックを実行
   */
  React.useEffect(() => {
    recreateListOnListModeChange(
      // listModeに変更があったときは今回のlistLModeをrefに保存
      listMode,
      listModeCache,
      () => {
        listModeCache.current = listMode;
        // listModeがお気に入りのときは一覧を再生成する
      },
      () => {
        setSyncBookmark(true);
        onSwitchToBookmarksList();
      },
    );
  }, [setSyncBookmark, listMode, onSwitchToBookmarksList]);

  /**
   * AllBookmarksが更新された時にListを更新する
   */
  const allBookmarksCache = React.useRef<string>(JSON.stringify(allBookmarks));
  const setAllBookmarksCache = React.useCallback((val: string) => {
    allBookmarksCache.current = val;
  }, []);
  React.useEffect(() => {
    (async () => {
      await onAllBookmarksChange(
        listModeCache,
        setAllBookmarksCache,
        allBookmarksCache,
        allBookmarks,
        dispatch,
        syncBookmark ? [] : list, // 同期時はallBookmarksで置き換えるためList(ローカルの一覧)は渡さない
      );
    })();
  }, [
    allBookmarks,
    dispatch,
    list,
    setAllBookmarksCache,
    report,
    syncBookmark,
  ]);

};

export default useBookmarksList;
