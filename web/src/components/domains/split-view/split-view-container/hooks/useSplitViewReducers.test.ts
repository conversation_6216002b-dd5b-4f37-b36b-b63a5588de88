import '@testing-library/jest-dom';
import { renderHook } from '@testing-library/react-hooks';
import { act } from 'react-dom/test-utils';
import { SplitViewDetailMessage, SplitViewDetailView } from '../../split-view-detail/SplitViewDetail';
import { SplitViewListMessage, SplitViewListView } from '../../split-view-list/SplitViewList';
import { ListMode } from '../../types/ListMode';
import { SplitViewReducerReturn } from '../reducers/SplitViewReducer';
import useSplitViewReducers, {
  UseSplitViewReducersReturnType, getActiveId,
} from './useSplitViewReducers';
import { createSplitViewListSingle } from '../../../../../utilities/test';
import mockMatchMedia from '../../../../../mocks/match-media';

jest.mock('../../../../../utilities/environment');

const matchMediaMock = mockMatchMedia();

describe('useSplitViewReducers', () => {
  beforeAll(() => {
    matchMediaMock.mockReturnValue({ matches: true });
  });

  function getHook() {

    return renderHook(() => useSplitViewReducers());
  }

  it('should return initial state', () => {
    const { result } = getHook();

    const {
      initialDisplayReducerReturn,
      bookmarksListReducerReturn,
      searchResultListReducerReturn,
      chatModeListReducerReturn,
      listModeStateReturn,
      isInitialDisplayMode,
      isBookmarksListMode,
      isSearchListMode,
    } = result.current;

    const [initialDisplayState] = initialDisplayReducerReturn;
    const [bookmarksState] = bookmarksListReducerReturn;
    const [searchResultState] = searchResultListReducerReturn;
    const [chatModeState] = chatModeListReducerReturn;
    const [listMode] = listModeStateReturn;

    const expectedInitialState = {
      listView: SplitViewListView.LOADING,
      listMessage: SplitViewListMessage.BLANK,
      list: [],
      activeId: '',
      detailView: SplitViewDetailView.LOADING,
      detailMessage: SplitViewDetailMessage.BLANK,
      detail: undefined,
      context: {
        sort: [],
        filter: [],
      },
      inlineMailAttachments: [],
      chatAttachments: [],
    };

    // AI用の初期状態の期待値
    const expectedChatModeInitialState = {
      listView: SplitViewListView.SEARCH_COMPLETED,
      listMessage: SplitViewListMessage.INITIAL_DISPLAY,
      list: [],
      activeId: '',
      detailView: SplitViewDetailView.ERROR,
      detailMessage: SplitViewDetailMessage.BLANK,
      detail: undefined,
      context: {
        sort: [],
        filter: [{ key: 'displayDate', option: 6 }],
      },
      inlineMailAttachments: [],
      chatAttachments: [],
    };

    expect(initialDisplayState).toStrictEqual(expectedInitialState);
    expect(bookmarksState).toStrictEqual(expectedInitialState);
    expect(searchResultState).toStrictEqual({
      ...expectedInitialState,
      detailView: SplitViewDetailView.ERROR,
      detailMessage: SplitViewDetailMessage.BLANK,
    });
    expect(chatModeState).toStrictEqual(expectedChatModeInitialState);
    expect(listMode).toBe(ListMode.INITIAL_DISPLAY);
    expect(isInitialDisplayMode).toBe(true);
    expect(isBookmarksListMode).toBe(false);
    expect(isSearchListMode).toBe(false);
  });

  describe('setListMode', () => {

    describe('when set BOOKMARKS', () => {
      const nextMode = ListMode.BOOKMARKS;

      it('should change the list mode', async () => {
        const { result } = getHook();
        const [, setListMode] = result.current.listModeStateReturn;

        await act(async () => {
          setListMode(nextMode);
        });

        const {
          listModeStateReturn,
          isInitialDisplayMode,
          isBookmarksListMode,
          isSearchListMode,
        } = result.current;

        expect(listModeStateReturn[0]).toBe(nextMode);
        expect(isInitialDisplayMode).toBe(false);
        expect(isBookmarksListMode).toBe(true);
        expect(isSearchListMode).toBe(false);
      });
    });
    describe('when set SEARCH', () => {
      const nextMode = ListMode.SEARCH;

      it('should change the list mode', async () => {
        const { result } = getHook();

        const [, setListMode] = result.current.listModeStateReturn;

        await act(async () => {
          setListMode(nextMode);
        });

        const {
          listModeStateReturn,
          isInitialDisplayMode,
          isSearchListMode,
        } = result.current;

        expect(listModeStateReturn[0]).toBe(nextMode);
        expect(isInitialDisplayMode).toBe(false);
        expect(isSearchListMode).toBe(true);
      });
    });
  });

  describe('getActiveId', () => {
    it('should get blank id', () => {
      expect(getActiveId({
        listView: SplitViewListView.LOADING,
        listMessage: SplitViewListMessage.BLANK,
        list: [createSplitViewListSingle({ id: '1' })],
        activeId: '',
        detailView: SplitViewDetailView.LOADING,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: undefined,
        context: {
          sort: [],
          filter: [],
        },
        inlineMailAttachments: [],
        chatAttachments: [],
      }, [
        createSplitViewListSingle({ id: '1' }),
      ], false)).toBe('');
    });

    it('should get first id', () => {
      expect(getActiveId({
        listView: SplitViewListView.LOADING,
        listMessage: SplitViewListMessage.BLANK,
        list: [createSplitViewListSingle({ id: '1' })],
        activeId: '',
        detailView: SplitViewDetailView.LOADING,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: undefined,
        context: {
          sort: [],
          filter: [],
        },
        inlineMailAttachments: [],
        chatAttachments: [],
      }, [
        createSplitViewListSingle({ id: '1' }),
      ], true)).toBe('1');
    });
  });

  describe('isDetailOpen', () => {
    describe('when the all of states do not have activeId', () => {
      const { result } = getHook();
      expect(result.current.isDetailOpen).toBe(false);
    });

    describe('when any of the state has activeId', () => {
      const reducerNames: (keyof UseSplitViewReducersReturnType)[] = [
        'initialDisplayReducerReturn',
        'searchResultListReducerReturn',
        'chatModeListReducerReturn',
      ];

      reducerNames.forEach((reducerName) => {
        describe(`case: ${reducerName}`, () => {
          it('should return true', async () => {
            const { result } = getHook();
            const [, dispatch] = result.current[reducerName] as SplitViewReducerReturn;

            await act(async () => {
              dispatch({
                type: 'SET_DATA',
                payload: { activeId: 'a' },
              });
            });

            expect(result.current.isDetailOpen).toBe(true);
          });
        });
      });
    });
  });

  describe('chatModeListReducerReturn', () => {
    // チャットモード用のreducerが正しく初期化されることをテスト
    it('チャットモード用のreducerが正しい初期状態を持つことを確認', () => {
      const { result } = getHook();
      const { chatModeListReducerReturn } = result.current;
      const [chatModeState] = chatModeListReducerReturn;

      const expectedChatModeInitialState = {
        listView: SplitViewListView.SEARCH_COMPLETED,
        listMessage: SplitViewListMessage.INITIAL_DISPLAY,
        list: [],
        activeId: '',
        detailView: SplitViewDetailView.ERROR,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: undefined,
        context: {
          sort: [],
          filter: [{ key: 'displayDate', option: 6 }],
        },
        inlineMailAttachments: [],
        chatAttachments: [],
      };

      expect(chatModeState).toStrictEqual(expectedChatModeInitialState);
    });

    // チャットモード用のreducerでSET_DATAアクションが正しく動作することをテスト
    it('チャットモード用のreducerでSET_DATAアクションが正しく動作することを確認', async () => {
      const { result } = getHook();
      const { chatModeListReducerReturn } = result.current;
      const [, dispatch] = chatModeListReducerReturn;

      await act(async () => {
        dispatch({
          type: 'SET_DATA',
          payload: { activeId: 'test-id', listView: SplitViewListView.DEFAULT },
        });
      });

      const [updatedState] = result.current.chatModeListReducerReturn;
      expect(updatedState.activeId).toBe('test-id');
      expect(updatedState.listView).toBe(SplitViewListView.DEFAULT);
    });

    // チャットモード用のreducerでSET_LISTアクションが正しく動作することをテスト
    it('チャットモード用のreducerでSET_LISTアクションが正しく動作することを確認', async () => {
      const { result } = getHook();
      const { chatModeListReducerReturn } = result.current;
      const [, dispatch] = chatModeListReducerReturn;

      const testList = [
        createSplitViewListSingle({ id: '1', title: 'Test Item 1' }),
        createSplitViewListSingle({ id: '2', title: 'Test Item 2' }),
      ];

      await act(async () => {
        dispatch({
          type: 'SET_LIST',
          payload: { list: testList },
        });
      });

      const [updatedState] = result.current.chatModeListReducerReturn;
      expect(updatedState.list).toStrictEqual(testList);
      expect(updatedState.listView).toBe(SplitViewListView.ON_INTERVAL);
    });

    // チャットモード用のreducerでSET_ACTIVEアクションが正しく動作することをテスト
    it('チャットモード用のreducerでSET_ACTIVEアクションが正しく動作することを確認', async () => {
      const { result } = getHook();
      const { chatModeListReducerReturn } = result.current;
      const [, dispatch] = chatModeListReducerReturn;

      await act(async () => {
        dispatch({
          type: 'SET_ACTIVE',
          payload: {
            activeId: 'active-test-id',
            title: 'Test Title',
            kind: 'SPO',
          },
        });
      });

      const [updatedState] = result.current.chatModeListReducerReturn;
      expect(updatedState.activeId).toBe('active-test-id');
      expect(updatedState.detail?.title).toBe('Test Title');
      expect(updatedState.detail?.kind).toBe('SPO');
    });

    // チャットモード用のreducerでUNSELECTアクションが正しく動作することをテスト
    it('チャットモード用のreducerでUNSELECTアクションが正しく動作することを確認', async () => {
      const { result } = getHook();
      const { chatModeListReducerReturn } = result.current;
      const [, dispatch] = chatModeListReducerReturn;

      // まずactiveIdを設定
      await act(async () => {
        dispatch({
          type: 'SET_ACTIVE',
          payload: {
            activeId: 'test-id',
            title: 'Test Title',
            kind: 'SPO',
          },
        });
      });

      // UNSELECTアクションを実行
      await act(async () => {
        dispatch({
          type: 'UNSELECT',
          payload: undefined,
        });
      });

      const [updatedState] = result.current.chatModeListReducerReturn;
      expect(updatedState.activeId).toBe('');
    });
  });
});
