import { createSplitViewListSingle } from '../../../../../utilities/test';
import { SplitViewListMessage, SplitViewListView } from '../../split-view-list/SplitViewList';
import { SplitViewDetailMessage, SplitViewDetailView } from '../../split-view-detail/SplitViewDetail';
import {
  initSplitViewState, initSplitViewStateForAI, ISplitViewState, splitViewReducer,
} from './SplitViewReducer';
// import { DataSourceKind } from '../../../../../types/DataSourceKind';

jest.mock('../../../../../utilities/environment');
const current = new Date(2023, 3, 1);
describe('splitViewReducer', () => {
  beforeAll(() => {
    jest.useFakeTimers('modern');
    jest.setSystemTime(current);
  });
  afterAll(() => {
    jest.useRealTimers();
  });

  describe('initSplitViewState', () => {
    it('should return the initial state', () => {
      expect(initSplitViewState()).toStrictEqual({
        listView: SplitViewListView.LOADING,
        listMessage: SplitViewListMessage.BLANK,
        list: [],
        activeId: '',
        detailView: SplitViewDetailView.LOADING,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [],
        chatAttachments: [],
      });
    });
  });

  describe('initSplitViewStateForAI', () => {
    // AI用の初期状態が正しく設定されることをテスト
    it('AI用の初期状態を正しく返すことを確認', () => {
      expect(initSplitViewStateForAI()).toStrictEqual({
        listView: SplitViewListView.SEARCH_COMPLETED,
        listMessage: SplitViewListMessage.INITIAL_DISPLAY,
        list: [],
        activeId: '',
        detailView: SplitViewDetailView.DEFAULT,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: undefined,
        context: {
          sort: [],
          filter: [{ key: 'displayDate', option: 6 }],
        },
        inlineMailAttachments: [],
        chatAttachments: [],
      });
    });

    // デフォルトの初期状態との違いを確認するテスト
    it('デフォルトの初期状態とAI用の初期状態の違いを確認', () => {
      const defaultState = initSplitViewState();
      const aiState = initSplitViewStateForAI();

      // 異なる部分を確認
      expect(aiState.listView).toBe(SplitViewListView.SEARCH_COMPLETED);
      expect(defaultState.listView).toBe(SplitViewListView.LOADING);

      expect(aiState.listMessage).toBe(SplitViewListMessage.INITIAL_DISPLAY);
      expect(defaultState.listMessage).toBe(SplitViewListMessage.BLANK);

      expect(aiState.detailView).toBe(SplitViewDetailView.DEFAULT);
      expect(defaultState.detailView).toBe(SplitViewDetailView.LOADING);

      expect(aiState.context.filter).toStrictEqual([{ key: 'displayDate', option: 6 }]);
      expect(defaultState.context.filter).toStrictEqual([]);

      // 共通部分を確認
      expect(aiState.list).toStrictEqual(defaultState.list);
      expect(aiState.activeId).toBe(defaultState.activeId);
      expect(aiState.detailMessage).toBe(defaultState.detailMessage);
      expect(aiState.detail).toBe(defaultState.detail);
      expect(aiState.context.sort).toStrictEqual(defaultState.context.sort);
      expect(aiState.inlineMailAttachments).toStrictEqual(defaultState.inlineMailAttachments);
      expect(aiState.chatAttachments).toStrictEqual(defaultState.chatAttachments);
    });
  });

  function createInitialState(override?: Partial<ISplitViewState>): ISplitViewState {
    return {
      listView: 'initial-view',
      listMessage: SplitViewListMessage.BLANK,
      list: [],
      activeId: '',
      detailView: SplitViewDetailView.LOADING,
      detailMessage: SplitViewDetailMessage.BLANK,
      detail: undefined,
      context: { sort: [], filter: [] },
      inlineMailAttachments: [],
      chatAttachments: [],
      ...override,
    };
  }

  describe('SET_DATA action', () => {
    it('should return a new state merged with the payload', () => {
      expect(splitViewReducer(
        createInitialState(),
        {
          type: 'SET_DATA',
          payload: {
            listView: 'modified-view',
          },
        },
      )).toStrictEqual({
        listView: 'modified-view',
        listMessage: SplitViewListMessage.BLANK,
        list: [],
        activeId: '',
        detailView: SplitViewDetailView.LOADING,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [],
        chatAttachments: [],
      });
    });
  });

  describe('SET_LIST/SET_LIST_WITH_DEFAULT action', () => {
    describe('when the action.type is SET_LIST', () => {
      it('should change the listView, list and should not change the activeId', () => {
        expect(splitViewReducer(createInitialState(), {
          type: 'SET_LIST',
          payload: {
            list: [createSplitViewListSingle({})],
          },
        }))
          .toStrictEqual(createInitialState({
            listView: SplitViewListView.ON_INTERVAL,
            list: [createSplitViewListSingle({})],
          }));
      });
    });

    describe('when the action.type is SET_LIST_WITH_DEFAULT', () => {
      describe('when the current activeId is set', () => {

        describe('when the new list has the old activeId', () => {
          it('should change the listView, list and should not change the activeId', () => {
            expect(splitViewReducer(createInitialState({ activeId: '123' }), {
              type: 'SET_LIST_WITH_DEFAULT',
              payload: {
                list: [createSplitViewListSingle({}), createSplitViewListSingle({ id: '123' })],
              },
            }))
              .toStrictEqual(createInitialState({
                listView: SplitViewListView.ON_INTERVAL,
                list: [createSplitViewListSingle({}), createSplitViewListSingle({ id: '123' })],
                // activeIdは古い状態を維持する
                activeId: '123',
              }));
          });
        });

        describe('when the new list does not have the old activeId', () => {
          it('should change the listView, list and should change the activeId', () => {
            expect(splitViewReducer(createInitialState({ activeId: 'hij' }), {
              type: 'SET_LIST_WITH_DEFAULT',
              payload: {
                list: [createSplitViewListSingle({ id: 'efg' }), createSplitViewListSingle({ id: '123' })],
              },
            }))
              .toStrictEqual(createInitialState({
                listView: SplitViewListView.ON_INTERVAL,
                list: [createSplitViewListSingle({ id: 'efg' }), createSplitViewListSingle({ id: '123' })],
                // activeIdは新しい一覧の1件目から取得される
                activeId: 'efg',
              }));
          });
        });
      });

      describe('when the current activeId is not set', () => {
        it('should change the listView, list and should set the activeId', () => {
          expect(splitViewReducer(createInitialState({ activeId: '' }), {
            type: 'SET_LIST_WITH_DEFAULT',
            payload: {
              list: [createSplitViewListSingle({ id: 'abcd' })],
            },
          }))
            .toStrictEqual(createInitialState({
              listView: SplitViewListView.ON_INTERVAL,
              list: [createSplitViewListSingle({ id: 'abcd' })],
              // activeIdは新しいlistからセットする
              activeId: 'abcd',
            }));
        });
      });
    });
  });

  describe('SET_ERROR action', () => {
    it('error the view and then overwrite the message value with payload.', () => {
      expect(splitViewReducer(
        {
          listView: SplitViewListView.DEFAULT,
          listMessage: SplitViewListMessage.API_REQUEST_FAIL,
          list: [],
          activeId: '',
          detailView: SplitViewDetailView.LOADING,
          detailMessage: SplitViewDetailMessage.API_REQUEST_FAIL,
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [],
          chatAttachments: [],
        },
        {
          type: 'SET_ERROR',
          payload: '通信エラー',
        },
      )).toStrictEqual({
        listView: SplitViewListView.ERROR,
        listMessage: '通信エラー',
        list: [],
        activeId: '',
        detailView: SplitViewDetailView.LOADING,
        detailMessage: SplitViewDetailMessage.API_REQUEST_FAIL,
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [],
        chatAttachments: [],
      });
    });
  });

  describe('SET_ACTIVE action', () => {
    it('activeIdが同じ場合は何もしない', () => {
      expect(splitViewReducer(
        {
          listView: 'active-view',
          listMessage: SplitViewListMessage.BLANK,
          list: [],
          activeId: '1234',
          detailView: SplitViewDetailView.DEFAULT,
          detailMessage: SplitViewDetailMessage.BLANK,
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [{
            id: 'aaaa',
            name: '',
            contentType: 'contentType',
            contentId: 'cid:aaaa',
            size: 100,
            isInline: true,
          }],
          chatAttachments: [{
            id: 'aaa',
            name: 'name',
            contentType: 'contentType',
            contentUrl: 'contentUrl',
            content: 'testContent',
          }],
        },
        {
          type: 'SET_ACTIVE',
          payload: {
            activeId: '1234',
            title: 'active',
            kind: 'SPO',
          },
        },
      )).toStrictEqual({
        listView: 'active-view',
        listMessage: SplitViewListMessage.BLANK,
        list: [],
        activeId: '1234',
        detailView: SplitViewDetailView.DEFAULT,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [{
          id: 'aaaa',
          name: '',
          contentType: 'contentType',
          contentId: 'cid:aaaa',
          size: 100,
          isInline: true,
        }],
        chatAttachments: [{
          id: 'aaa',
          name: 'name',
          contentType: 'contentType',
          contentUrl: 'contentUrl',
          content: 'testContent',
        }],
      });
    });

    it('activeIdが異なる場合、detailを上書きする', () => {
      expect(splitViewReducer(
        {
          listView: 'active-view',
          listMessage: SplitViewDetailMessage.BLANK,
          list: [],
          activeId: '1234',
          detailView: SplitViewDetailView.DEFAULT,
          detailMessage: SplitViewDetailMessage.BLANK,
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [{
            id: 'aaaa',
            name: '',
            contentType: 'contentType',
            contentId: 'cid:aaaa',
            size: 100,
            isInline: true,
          }],
          chatAttachments: [{
            id: 'aaa',
            name: 'name',
            contentType: 'contentType',
            contentUrl: 'contentUrl',
            content: 'testContent',
          }],
        },
        {
          type: 'SET_ACTIVE',
          payload: {
            activeId: '12345',
            title: 'active',
            kind: 'SPO',
          },
        },
      )).toStrictEqual({
        listView: 'active-view',
        listMessage: SplitViewListMessage.BLANK,
        list: [],
        activeId: '12345',
        detailView: SplitViewDetailView.LOADING,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: {
          id: '12345',
          title: 'active',
          kind: 'SPO',
        },
        context: { sort: [], filter: [] },
        inlineMailAttachments: [],
        chatAttachments: [],
      });
    });
  });

  describe('UNSELECT action', () => {
    it('activeIdに空の文字列を上書きする', () => {
      expect(splitViewReducer(
        {
          listView: 'unselect-view',
          listMessage: SplitViewListMessage.BLANK,
          list: [],
          activeId: '1234',
          detailView: SplitViewDetailView.DEFAULT,
          detailMessage: SplitViewDetailMessage.BLANK,
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [],
          chatAttachments: [],
        },
        {
          type: 'UNSELECT',
          payload: undefined,
        },
      )).toStrictEqual({
        listView: 'unselect-view',
        listMessage: SplitViewListMessage.BLANK,
        list: [],
        activeId: '',
        detailView: SplitViewDetailView.ERROR,
        detailMessage: SplitViewDetailMessage.NOT_SELECTED,
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [],
        chatAttachments: [],
      });
    });
  });

  describe('SET_DETAIL_ERROR action', () => {
    it('detailViewをERRORにして、かつdetailMessageをpayloadの値で置き換えたオブジェクトを返却する', () => {
      expect(splitViewReducer(
        {
          listView: SplitViewListView.DEFAULT,
          listMessage: SplitViewListMessage.BLANK,
          list: [],
          activeId: '',
          detailView: SplitViewDetailView.DEFAULT,
          detailMessage: SplitViewDetailMessage.BLANK,
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [],
          chatAttachments: [],
        },
        {
          type: 'SET_DETAIL_ERROR',
          payload: 'abcd',
        },
      )).toStrictEqual({
        listView: SplitViewListView.DEFAULT,
        listMessage: SplitViewListMessage.BLANK,
        list: [],
        activeId: '',
        detailView: SplitViewDetailView.ERROR,
        detailMessage: 'abcd',
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [],
        chatAttachments: [],
      });
    });
  });

  describe('SET_DETAIL action', () => {
    // TODO:index仕様が変更されたら元に戻す
    // it('activeIdが異なる場合は何もしない', () => {
    //   expect(splitViewReducer(
    //     {
    //       listView: SplitViewListView.DEFAULT,
    //       listMessage: SplitViewListMessage.BLANK,
    //       list: [],
    //       activeId: '1234',
    //       detailView: SplitViewDetailView.LOADING,
    //       detailMessage: SplitViewDetailMessage.BLANK,
    //       detail: undefined,
    //       context: { sort: [], filter: [] },
    //       inlineMailAttachments: [],
    //       chatAttachments: [],
    //     },
    //     {
    //       type: 'SET_DETAIL',
    //       payload: {
    //         detail: {
    //           id: 'abcd',
    //           kind: DataSourceKind.Other,
    //           title: '',
    //         },
    //       },
    //     },
    //   )).toStrictEqual({
    //     listView: SplitViewListView.DEFAULT,
    //     listMessage: SplitViewListMessage.BLANK,
    //     list: [],
    //     activeId: '1234',
    //     detailView: SplitViewDetailView.LOADING,
    //     detailMessage: SplitViewDetailMessage.BLANK,
    //     detail: undefined,
    //     context: { sort: [], filter: [] },
    //     inlineMailAttachments: [],
    //     chatAttachments: [],
    //   });
    // });

    it('activeIdが同じ場合、SplitViewDetailViewをDEFAULTにし、detailとattachmentsが存在する場合にstateに上書きをする', () => {
      expect(splitViewReducer(
        {
          listView: SplitViewListView.DEFAULT,
          listMessage: SplitViewListMessage.BLANK,
          list: [],
          activeId: '1234',
          detailView: SplitViewDetailView.LOADING,
          detailMessage: SplitViewDetailMessage.BLANK,
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [],
          chatAttachments: [],
        },
        {
          type: 'SET_DETAIL',
          payload: {
            detail: {
              id: '1234',
              kind: 'Other',
              title: 'title',
              properties: {
                spoAttachments: [
                  {
                    id: '',
                    extension: '',
                    title: '12345',
                  },
                  {
                    id: '',
                    extension: '',
                    url: 'abcd',
                  },
                ],
              },
            },
          },
        },
      )).toStrictEqual({
        listView: SplitViewListView.DEFAULT,
        listMessage: SplitViewListMessage.BLANK,
        list: [],
        activeId: '1234',
        detailView: SplitViewDetailView.DEFAULT,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: {
          id: '1234',
          kind: 'Other',
          title: 'title',
          properties: {
            spoAttachments: [
              {
                id: '',
                extension: '',
                title: '12345',
              },
              {
                id: '',
                extension: '',
                url: 'abcd',
              },
            ],
          },
        },
        context: { sort: [], filter: [] },
        inlineMailAttachments: [],
        chatAttachments: [],
      });
    });

    it('activeIdが同じ場合、SplitViewDetailViewをDEFAULTにし、detailとattachmentsが存在しない場合にstateに上書きをする', () => {
      expect(splitViewReducer(
        {
          listView: SplitViewListView.DEFAULT,
          listMessage: SplitViewListMessage.BLANK,
          list: [],
          activeId: '1234',
          detailView: SplitViewDetailView.LOADING,
          detailMessage: SplitViewDetailMessage.BLANK,
          detail: {
            id: '1234',
            title: 'title1',
            kind: 'SPO',
            displayDate: 'a',
            properties: {
              spoAttachments: [{
                id: '',
                extension: '',
                title: '12345',
              },
              {
                id: '',
                extension: '',
                url: 'abcd',
              }],
            },
          },
          context: { sort: [], filter: [] },
          inlineMailAttachments: [],
          chatAttachments: [],
        },
        {
          type: 'SET_DETAIL',
          payload: {
            detail: {
              id: '1234',
              displayDate: 'b',
              title: 'title2',
              kind: 'SPO',
            },
          },
        },
      )).toStrictEqual({
        listView: SplitViewListView.DEFAULT,
        listMessage: SplitViewListMessage.BLANK,
        list: [],
        activeId: '1234',
        detailView: SplitViewDetailView.DEFAULT,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: {
          id: '1234',
          displayDate: 'b',
          title: 'title2',
          kind: 'SPO',
        },
        context: { sort: [], filter: [] },
        inlineMailAttachments: [],
        chatAttachments: [],
      });
    });

    describe('when the state content and payload content have the same id and updatedDate(displayDate)', () => {
      it('should prevent state changing', () => {
        expect(splitViewReducer({
          listView: SplitViewListView.DEFAULT,
          listMessage: SplitViewListMessage.BLANK,
          list: [],
          activeId: '1',
          detailView: SplitViewDetailView.LOADING,
          detailMessage: SplitViewDetailMessage.BLANK,
          detail: {
            id: '1',
            kind: 'SPO',
            title: 'title1',
            body: 'abc',
            displayDate: '2020-01-01T12:00:00.000Z',
          },
          context: { sort: [], filter: [] },
          inlineMailAttachments: [],
          chatAttachments: [],
        }, {
          type: 'SET_DETAIL',
          payload: {
            detail: {
              id: '1',
              kind: 'Other',
              title: 'title2',
              body: 'efg',
              displayDate: '2020-01-01T12:00:00.000Z',
            },
          },
        })).toStrictEqual({
          listView: SplitViewListView.DEFAULT,
          listMessage: SplitViewListMessage.BLANK,
          list: [],
          activeId: '1',
          detailView: SplitViewDetailView.LOADING,
          detailMessage: SplitViewDetailMessage.BLANK,
          detail: {
            id: '1',
            body: 'abc',
            kind: 'SPO',
            title: 'title1',
            displayDate: '2020-01-01T12:00:00.000Z',
          },
          context: { sort: [], filter: [] },
          inlineMailAttachments: [],
          chatAttachments: [],
        });
      });
    });
  });

  describe('SET_NO_ITEMS action', () => {
    it('should return blank state to show message', () => {
      expect(splitViewReducer(createInitialState({
        detail: {
          id: 'abc',
          kind: 'SPO',
          title: 'title1',
        },
        activeId: 'abc',
        list: [{
          id: 'abc',
          kind: 'SPO',
          title: 'test1',
          note: 'test2',
          displayDate: 'test3',
          properties: {},
        }],
      }), {
        type: 'SET_NO_ITEMS',
        payload: {
          listMessage: SplitViewListMessage.NO_SEARCH_RESULT,
          detailMessage: SplitViewDetailMessage.NOT_SELECTED,
        },
      })).toStrictEqual(createInitialState({
        detail: undefined,
        activeId: '',
        list: [],
        listView: SplitViewListView.SEARCH_COMPLETED,
        listMessage: SplitViewListMessage.NO_SEARCH_RESULT,
        detailView: SplitViewDetailView.ERROR,
        detailMessage: SplitViewDetailMessage.NOT_SELECTED,
        context: { sort: [], filter: [] },
      }));
    });
  });

  describe('SET_INLINE_MAIL_ATTACHMENTS action', () => {
    it('inlineMailAttachmentsが空の時は追加する', () => {
      expect(splitViewReducer(
        {
          listView: 'active-view',
          listMessage: SplitViewListMessage.BLANK,
          list: [],
          activeId: '1234',
          detailView: SplitViewDetailView.DEFAULT,
          detailMessage: SplitViewDetailMessage.BLANK,
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [],
          chatAttachments: [],
        },
        {
          type: 'SET_INLINE_MAIL_ATTACHMENTS',
          payload: {
            inlineMailAttachments: [{
              id: 'aaaa',
              name: '',
              contentType: 'contentType',
              contentId: 'cid:aaaa',
              size: 100,
              isInline: true,
            }],
          },
        },
      )).toStrictEqual({
        listView: 'active-view',
        listMessage: SplitViewListMessage.BLANK,
        list: [],
        activeId: '1234',
        detailView: SplitViewDetailView.DEFAULT,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [{
          id: 'aaaa',
          name: '',
          contentType: 'contentType',
          contentId: 'cid:aaaa',
          size: 100,
          isInline: true,
        }],
        chatAttachments: [],
      });
    });

    it('inlineMailAttachmentsに既に値がセットされている場合', () => {
      expect(splitViewReducer(
        {
          listView: 'active-view',
          listMessage: SplitViewListMessage.BLANK,
          list: [],
          activeId: '1234',
          detailView: SplitViewDetailView.DEFAULT,
          detailMessage: SplitViewDetailMessage.BLANK,
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [{
            id: 'aaaa',
            name: '',
            contentType: 'contentType',
            contentId: 'cid:aaaa',
            size: 100,
            isInline: true,
          }],
          chatAttachments: [],
        },
        {
          type: 'SET_INLINE_MAIL_ATTACHMENTS',
          payload: {
            inlineMailAttachments: [{
              id: 'bbbb',
              name: '',
              contentType: 'contentType',
              contentId: 'cid:bbbb',
              size: 100,
              isInline: true,
            }],
          },
        },
      )).toStrictEqual({
        listView: 'active-view',
        listMessage: SplitViewListMessage.BLANK,
        list: [],
        activeId: '1234',
        detailView: SplitViewDetailView.DEFAULT,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [{
          id: 'bbbb',
          name: '',
          contentType: 'contentType',
          contentId: 'cid:bbbb',
          size: 100,
          isInline: true,
        }],
        chatAttachments: [],
      });
    });

    it('inlineMailAttachmentsに重複する値がセットされている場合', () => {
      expect(splitViewReducer(
        {
          listView: 'active-view',
          listMessage: SplitViewListMessage.BLANK,
          list: [],
          activeId: '1234',
          detailView: SplitViewDetailView.DEFAULT,
          detailMessage: SplitViewDetailMessage.BLANK,
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [{
            id: 'aaaa',
            name: '',
            contentType: 'contentType',
            contentId: 'cid:aaaa',
            size: 100,
            isInline: true,
          }],
          chatAttachments: [],
        },
        {
          type: 'SET_INLINE_MAIL_ATTACHMENTS',
          payload: {
            inlineMailAttachments: [{
              id: 'aaaa',
              name: '',
              contentType: 'contentType',
              contentId: 'cid:aaaa',
              size: 100,
              isInline: true,
            }],
          },
        },
      )).toStrictEqual({
        listView: 'active-view',
        listMessage: SplitViewListMessage.BLANK,
        list: [],
        activeId: '1234',
        detailView: SplitViewDetailView.DEFAULT,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [{
          id: 'aaaa',
          name: '',
          contentType: 'contentType',
          contentId: 'cid:aaaa',
          size: 100,
          isInline: true,
        }],
        chatAttachments: [],
      });
    });
  });

  describe('SET_CHAT_ATTACHMENTS action', () => {
    it('chatAttachmentsが空の時は追加する', () => {
      expect(splitViewReducer(
        {
          listView: 'active-view',
          listMessage: SplitViewListMessage.BLANK,
          list: [],
          activeId: '1234',
          detailView: SplitViewDetailView.DEFAULT,
          detailMessage: SplitViewDetailMessage.BLANK,
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [],
          chatAttachments: [],
        },
        {
          type: 'SET_CHAT_ATTACHMENTS',
          payload: {
            chatAttachments: [{
              id: 'aaa',
              name: 'name',
              contentType: 'contentType',
              contentUrl: 'contentUrl',
              content: 'testContent',
            }],
          },
        },
      )).toStrictEqual({
        listView: 'active-view',
        listMessage: SplitViewListMessage.BLANK,
        list: [],
        activeId: '1234',
        detailView: SplitViewDetailView.DEFAULT,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [],
        chatAttachments: [{
          id: 'aaa',
          name: 'name',
          contentType: 'contentType',
          contentUrl: 'contentUrl',
          content: 'testContent',
        }],
      });
    });

    it('chatAttachmentsに既に値がセットされている場合', () => {
      expect(splitViewReducer(
        {
          listView: 'active-view',
          listMessage: SplitViewListMessage.BLANK,
          list: [],
          activeId: '1234',
          detailView: SplitViewDetailView.DEFAULT,
          detailMessage: SplitViewDetailMessage.BLANK,
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [],
          chatAttachments: [{
            id: 'aaa',
            name: 'name',
            contentType: 'contentType',
            contentUrl: 'contentUrl',
            content: 'testContent1',
          }],
        },
        {
          type: 'SET_CHAT_ATTACHMENTS',
          payload: {
            chatAttachments: [{
              id: 'bbb',
              name: 'name',
              contentType: 'contentType',
              contentUrl: 'contentUrl',
              content: 'testContent2',
            }],
          },
        },
      )).toStrictEqual({
        listView: 'active-view',
        listMessage: SplitViewListMessage.BLANK,
        list: [],
        activeId: '1234',
        detailView: SplitViewDetailView.DEFAULT,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [],
        chatAttachments: [{
          id: 'bbb',
          name: 'name',
          contentType: 'contentType',
          contentUrl: 'contentUrl',
          content: 'testContent2',
        }],
      });
    });

    it('chatAttachmentsに重複する値がセットされている場合', () => {
      expect(splitViewReducer(
        {
          listView: 'active-view',
          listMessage: SplitViewListMessage.BLANK,
          list: [],
          activeId: '1234',
          detailView: SplitViewDetailView.DEFAULT,
          detailMessage: SplitViewDetailMessage.BLANK,
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [],
          chatAttachments: [{
            id: 'aaa',
            name: 'name',
            contentType: 'contentType',
            contentUrl: 'contentUrl',
            content: 'testContent',
          }],
        },
        {
          type: 'SET_CHAT_ATTACHMENTS',
          payload: {
            chatAttachments: [{
              id: 'aaa',
              name: 'name',
              contentType: 'contentType',
              contentUrl: 'contentUrl',
              content: 'testContent',
            }],
          },
        },
      )).toStrictEqual({
        listView: 'active-view',
        listMessage: SplitViewListMessage.BLANK,
        list: [],
        activeId: '1234',
        detailView: SplitViewDetailView.DEFAULT,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [],
        chatAttachments: [{
          id: 'aaa',
          name: 'name',
          contentType: 'contentType',
          contentUrl: 'contentUrl',
          content: 'testContent',
        }],
      });
    });
  });

  describe('SET_LISTVIEW action', () => {
    it('SplitViewListView = loading', () => {
      expect(splitViewReducer(
        {
          listView: SplitViewListView.DEFAULT,
          listMessage: '',
          list: [],
          activeId: '',
          detailView: '',
          detailMessage: '',
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [],
          chatAttachments: [],
        },
        {
          type: 'SET_LISTVIEW',
          payload: {
            listView: 'loading',
          },
        },
      )).toStrictEqual({
        listView: SplitViewListView.LOADING,
        listMessage: '',
        list: [],
        activeId: '',
        detailView: '',
        detailMessage: '',
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [],
        chatAttachments: [],
      });
    });

    it('SplitViewListView = default', () => {
      expect(splitViewReducer(
        {
          listView: SplitViewListView.LOADING,
          listMessage: '',
          list: [],
          activeId: '',
          detailView: '',
          detailMessage: '',
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [],
          chatAttachments: [],
        },
        {
          type: 'SET_LISTVIEW',
          payload: {
            listView: 'default',
          },
        },
      )).toStrictEqual({
        listView: SplitViewListView.DEFAULT,
        listMessage: '',
        list: [],
        activeId: '',
        detailView: '',
        detailMessage: '',
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [],
        chatAttachments: [],
      });
    });

    it('SplitViewListView = error', () => {
      expect(splitViewReducer(
        {
          listView: SplitViewListView.DEFAULT,
          listMessage: '',
          list: [],
          activeId: '',
          detailView: '',
          detailMessage: '',
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [],
          chatAttachments: [],
        },
        {
          type: 'SET_LISTVIEW',
          payload: {
            listView: 'error',
          },
        },
      )).toStrictEqual({
        listView: SplitViewListView.ERROR,
        listMessage: '',
        list: [],
        activeId: '',
        detailView: '',
        detailMessage: '',
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [],
        chatAttachments: [],
      });
    });

    it('SplitViewListView = onInterval', () => {
      expect(splitViewReducer(
        {
          listView: SplitViewListView.DEFAULT,
          listMessage: '',
          list: [],
          activeId: '',
          detailView: '',
          detailMessage: '',
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [],
          chatAttachments: [],
        },
        {
          type: 'SET_LISTVIEW',
          payload: {
            listView: 'onInterval',
          },
        },
      )).toStrictEqual({
        listView: SplitViewListView.ON_INTERVAL,
        listMessage: '',
        list: [],
        activeId: '',
        detailView: '',
        detailMessage: '',
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [],
        chatAttachments: [],
      });
    });

    it('SplitViewListView = searchCompleted', () => {
      expect(splitViewReducer(
        {
          listView: SplitViewListView.DEFAULT,
          listMessage: '',
          list: [],
          activeId: '',
          detailView: '',
          detailMessage: '',
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [],
          chatAttachments: [],
        },
        {
          type: 'SET_LISTVIEW',
          payload: {
            listView: 'searchCompleted',
          },
        },
      )).toStrictEqual({
        listView: SplitViewListView.SEARCH_COMPLETED,
        listMessage: '',
        list: [],
        activeId: '',
        detailView: '',
        detailMessage: '',
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [],
        chatAttachments: [],
      });
    });
  });

  describe('SET_DETAILVIEW action', () => {
    it('SplitViewDetailView = loading', () => {
      expect(splitViewReducer(
        {
          listView: '',
          listMessage: '',
          list: [],
          activeId: '',
          detailView: SplitViewDetailView.DEFAULT,
          detailMessage: '',
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [],
          chatAttachments: [],
        },
        {
          type: 'SET_DETAILVIEW',
          payload: {
            detailView: 'loading',
          },
        },
      )).toStrictEqual({
        listView: '',
        listMessage: '',
        list: [],
        activeId: '',
        detailView: SplitViewDetailView.LOADING,
        detailMessage: '',
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [],
        chatAttachments: [],
      });
    });

    it('SplitViewDetailView = default', () => {
      expect(splitViewReducer(
        {
          listView: '',
          listMessage: '',
          list: [],
          activeId: '',
          detailView: SplitViewDetailView.LOADING,
          detailMessage: '',
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [],
          chatAttachments: [],
        },
        {
          type: 'SET_DETAILVIEW',
          payload: {
            detailView: 'default',
          },
        },
      )).toStrictEqual({
        listView: '',
        listMessage: '',
        list: [],
        activeId: '',
        detailView: SplitViewDetailView.DEFAULT,
        detailMessage: '',
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [],
        chatAttachments: [],
      });
    });

    it('SplitViewDetailView = error', () => {
      expect(splitViewReducer(
        {
          listView: '',
          listMessage: '',
          list: [],
          activeId: '',
          detailView: SplitViewDetailView.DEFAULT,
          detailMessage: '',
          detail: undefined,
          context: { sort: [], filter: [] },
          inlineMailAttachments: [],
          chatAttachments: [],
        },
        {
          type: 'SET_DETAILVIEW',
          payload: {
            detailView: 'error',
            detailMessage: 'error message',
          },
        },
      )).toStrictEqual({
        listView: '',
        listMessage: '',
        list: [],
        activeId: '',
        detailView: SplitViewDetailView.ERROR,
        detailMessage: 'error message',
        detail: undefined,
        context: { sort: [], filter: [] },
        inlineMailAttachments: [],
        chatAttachments: [],
      });
    });
  });

  describe('SET_SORT action', () => {
    beforeAll(() => {
      jest.useFakeTimers('modern');
      jest.setSystemTime(current);
    });
    afterAll(() => {
      jest.useRealTimers();
    });

    it('should update new sort condition', () => {
      expect(splitViewReducer(
        {
          listView: SplitViewListView.DEFAULT,
          listMessage: '',
          list: [],
          activeId: 'oldActiveId',
          detailView: '',
          detailMessage: '',
          detail: undefined,
          context: {
            sort: [{
              key: 'displayDate',
              order: 'asc',
              priority: 1,
            }],
            filter: [],
            timestamp: current,
          },
          inlineMailAttachments: [],
          chatAttachments: [],
        },
        {
          type: 'SET_SORT',
          payload: {
            sort: [{
              key: 'displayDate',
              order: 'desc',
              priority: 1,
            }],
          },
        },
      )).toStrictEqual({
        listView: SplitViewListView.DEFAULT,
        listMessage: '',
        list: [],
        activeId: '',
        detailView: '',
        detailMessage: '',
        detail: undefined,
        context: {
          sort: [{
            key: 'displayDate',
            order: 'desc',
            priority: 1,
          }],
          filter: [],
          timestamp: current,
        },
        inlineMailAttachments: [],
        chatAttachments: [],
      });
    });

  });

  describe('SET_FILTER action', () => {
    beforeAll(() => {
      jest.useFakeTimers('modern');
      jest.setSystemTime(current);
    });
    afterAll(() => {
      jest.useRealTimers();
    });

    it('should update new filter condition', () => {
      expect(splitViewReducer(
        {
          listView: SplitViewListView.DEFAULT,
          listMessage: '',
          list: [],
          activeId: 'oldActiveId',
          detailView: '',
          detailMessage: '',
          detail: undefined,
          context: {
            sort: [{
              key: 'displayDate',
              order: 'asc',
              priority: 1,
            }],
            filter: [],
            timestamp: current,
          },
          inlineMailAttachments: [],
          chatAttachments: [],
        },
        {
          type: 'SET_FILTER',
          payload: {
            filter: [{
              key: 'displayDate',
              option: 2,
            }],
          },
        },
      )).toStrictEqual({
        listView: SplitViewListView.DEFAULT,
        listMessage: '',
        list: [],
        activeId: '',
        detailView: '',
        detailMessage: '',
        detail: undefined,
        context: {
          sort: [{
            key: 'displayDate',
            order: 'asc',
            priority: 1,
          }],
          filter: [
            {
              key: 'displayDate',
              option: 2,
            },
          ],
          timestamp: current,
        },
        inlineMailAttachments: [],
        chatAttachments: [],
      });
    });
  });
});
