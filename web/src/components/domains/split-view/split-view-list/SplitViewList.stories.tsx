import React from 'react';
import { StoryFn, Meta } from '@storybook/react';
import {
  setCommonArgTypes,
  setStoryMockCommonArgTypes,
  setStoryWidthHeight,
  setSelectOptions,
  StoryMockProps, cloneForMobileView,
} from '../../../../../.storybook/utils';
import { ListMode } from '../types/ListMode';
import SplitViewList, { ISplitViewListProps, SplitViewListMessage, SplitViewListView } from './SplitViewList';
import { ISortOrder } from '../../../../types/IContext';
import { ISplitViewState } from '../split-view-container/reducers/SplitViewReducer';

export default {
  title: 'split-view/SplitViewList.tsx',
  component: SplitViewList,
  argTypes: {
    onClickItem: { action: 'clicked' },
    ...setStoryMockCommonArgTypes(1000, 500),
    ...setCommonArgTypes(SplitViewListView, SplitViewListMessage),
    listMode: setSelectOptions(ListMode),
  },
} as Meta;

const Template: StoryFn<StoryMockProps<ISplitViewListProps>> = (args) => (
  <div {...setStoryWidthHeight(args)}>
    <div style={{ overflow: 'hidden', height: '100%' }}>
      <SplitViewList {...args} />
    </div>
  </div>
);

// FilterDropdownとDataSourceFilterDropdown用のダミーstate
const dummyState: ISplitViewState = {
  list: [],
  context: {
    sort: [] as ISortOrder[],
    filter: [
      { key: 'displayDate', option: 0 }, // IDisplayDateFilterOption
      { key: 'kind', option: [] }, // IKindFilterOption
    ],
    timestamp: new Date(),
    displayIds: [],
  },
  listView: '',
  listMessage: '',
  activeId: '',
  detailView: '',
  detailMessage: '',
  detail: undefined,
  inlineMailAttachments: [],
  chatAttachments: [],
};

export const Default: StoryFn<StoryMockProps<ISplitViewListProps>> = Template.bind({});
Default.args = {
  view: SplitViewListView.SEARCH_COMPLETED,
  storyWidth: 560,
  storyHeight: 925,
  message: SplitViewListMessage.BLANK,
  appTitle: 'atTane',
  state: dummyState,
  listMode: ListMode.SEARCH,
  items: [
    {
      id: 'ef8f1072-30bb-4492-a0b9-b0b4edb17ca8',
      displayDate: new Date('2021-10-23').toISOString(),
      title: '第××期（２０２１年度）決算のご報告_0020',
      note: '分類名1',
      kind: 'SPO',
      properties: {
        hasAttachments: true,
        createdDate: '2021-08-24T03:00:59Z',
      },
    }, {
      id: 'abcd',
      displayDate: '2021-08-24T03:00:59Z',
      title: 'テスト記事タイトル',
      note: 'あいうえお',
      kind: 'SPO',
      properties: {
        hasAttachments: false,
        createdDate: '2021-08-24T03:00:59Z',
      },
    }, {
      id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
      displayDate: '2021-08-24T03:00:59Z',
      title: '2021年度社内通知（第11回）_0019',
      note: 'テスト記事タイトル',
      kind: 'SPO',
      properties: {
        hasAttachments: true,
        createdDate: '2021-08-24T02:39:48Z',
      },
    }, {
      id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
      displayDate: '2021-08-24T03:00:59Z',
      title: '2021年度社内通知（第13回）',
      note: 'テスト記事タイトル',
      kind: 'SPO',
      properties: {
        hasAttachments: false,
        createdDate: '2021-08-24T02:39:48Z',
      },
    }, {
      id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
      displayDate: '2021-08-24T03:00:59Z',
      title: '2021年度社内通知（第14回）',
      note: 'テスト記事タイトル',
      kind: 'SPO',
      properties: {
        hasAttachments: false,
        createdDate: '2021-08-24T02:39:48Z',
      },
    }, {
      id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
      displayDate: '2021-08-24T03:00:59Z',
      title: '2021年度社内通知（第15回）',
      note: 'テスト記事タイトル',
      kind: 'SPO',
      properties: {
        hasAttachments: false,
        createdDate: '2021-08-24T02:39:48Z',
      },
    }, {
      id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
      displayDate: '2021-08-24T03:00:59Z',
      title: '2021年度社内通知（第16回）',
      note: 'テスト記事タイトル',
      kind: 'SPO',
      properties: {
        hasAttachments: false,
        createdDate: '2021-08-24T02:39:48Z',
      },
    }, {
      id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
      displayDate: '2021-08-24T03:00:59Z',
      title: '2021年度社内通知（第17回）',
      note: 'テスト記事タイトル',
      kind: 'SPO',
      properties: {
        hasAttachments: false,
        createdDate: '2021-08-24T02:39:48Z',
      },
    }, {
      id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
      displayDate: '2021-08-24T03:00:59Z',
      title: '2021年度社内通知（第18回）',
      note: 'テスト記事タイトル',
      kind: 'SPO',
      properties: {
        hasAttachments: false,
        createdDate: '2021-08-24T02:39:48Z',
      },
    }, {
      id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
      displayDate: '2021-08-24T03:00:59Z',
      title: '2021年度社内通知（第19回）',
      note: 'テスト記事タイトル',
      kind: 'SPO',
      properties: {
        hasAttachments: false,
        createdDate: '2021-08-24T02:39:48Z',
      },
    }, {
      id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
      displayDate: '2021-08-24T03:00:59Z',
      title: '2021年度社内通知（第20回）',
      note: 'テスト記事タイトル',
      kind: 'SPO',
      properties: {
        hasAttachments: false,
        createdDate: '2021-08-24T02:39:48Z',
      },
    }, {
      id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
      displayDate: '2021-08-24T03:00:59Z',
      title: '2021年度社内通知（第21回）',
      note: 'テスト記事タイトル',
      kind: 'SPO',
      properties: {
        hasAttachments: false,
        createdDate: '2021-08-24T02:39:48Z',
      },
    }, {
      id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
      displayDate: '2021-08-24T03:00:59Z',
      title: '2021年度社内通知（第22回）',
      note: 'テスト記事タイトル',
      kind: 'SPO',
      properties: {
        hasAttachments: false,
        createdDate: '2021-08-24T02:39:48Z',
      },
    }, {
      id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
      displayDate: '2021-08-24T03:00:59Z',
      title: '2021年度社内通知（第23回）',
      note: 'テスト記事タイトル',
      kind: 'SPO',
      properties: {
        hasAttachments: false,
        createdDate: '2021-08-24T02:39:48Z',
      },
    }, {
      id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
      displayDate: '2021-08-24T03:00:59Z',
      title: '2021年度社内通知（第24回）',
      note: 'テスト記事タイトル',
      kind: 'SPO',
      properties: {
        hasAttachments: false,
        createdDate: '2021-08-24T02:39:48Z',
      },
    }, {
      id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
      displayDate: '2021-08-24T03:00:59Z',
      title: '2021年度社内通知（第25回）',
      note: 'テスト記事タイトル',
      kind: 'SPO',
      properties: {
        hasAttachments: false,
        createdDate: '2021-08-24T02:39:48Z',
      },
    },
  ],
};

export const DefaultOnMobile: StoryFn<StoryMockProps<ISplitViewListProps>> = cloneForMobileView(
  Template,
  Default,
);

export const Loading: StoryFn<StoryMockProps<ISplitViewListProps>> = Template.bind({});
Loading.args = {
  view: SplitViewListView.LOADING,
  storyWidth: 560,
  storyHeight: 925,
  message: SplitViewListMessage.BLANK,
  items: [],
};
export const LoadingOnMobile: StoryFn<StoryMockProps<ISplitViewListProps>> = cloneForMobileView(
  Template,
  Loading,
);

export const Error: StoryFn<StoryMockProps<ISplitViewListProps>> = Template.bind({});
Error.args = {
  view: SplitViewListView.ERROR,
  storyWidth: 560,
  storyHeight: 925,
  message: SplitViewListMessage.API_REQUEST_FAIL,
  items: [],
  appTitle: 'atTane',
};
export const ErrorOnMobile: StoryFn<StoryMockProps<ISplitViewListProps>> = cloneForMobileView(
  Template,
  Error,
);

export const Bookmarks: StoryFn<StoryMockProps<ISplitViewListProps>> = Template.bind({});
Bookmarks.args = {
  ...Default.args,
  view: SplitViewListView.DEFAULT,
  listMode: ListMode.BOOKMARKS,
  state: dummyState,
};
export const BookmarksOnMobile: StoryFn<StoryMockProps<ISplitViewListProps>> = cloneForMobileView(
  Template,
  Bookmarks,
);

export const SearchResult: StoryFn<StoryMockProps<ISplitViewListProps>> = Template.bind({});
SearchResult.args = {
  ...Default.args,
  view: SplitViewListView.DEFAULT,
  listMode: ListMode.SEARCH,
};
export const SearchResultOnMobile: StoryFn<
  StoryMockProps<ISplitViewListProps>
> = cloneForMobileView(
  Template,
  SearchResult,
);
