import React from 'react';
import PropTypes from 'prop-types';
import Scrollbars from 'react-custom-scrollbars-2';
import {
  <PERSON>, Loader, Button,
} from '@fluentui/react-northstar';
import { useUpdate } from 'react-use';
import { EventReporter } from '@avanade-teams/app-insights-reporter';
import { AsyncState } from 'react-use/lib/useAsync';
import { ValueOf } from '../../../../utilities/type';
import { splitWithSeparator } from '../../../../utilities/text';
import { mergedClassName } from '../../../../utilities/commonFunction';
import FeaturesFlags from '../../../../utilities/const/featuresFlag';
import CancellationButton from '../../../commons/atoms/cancellation-button/CancellationButton';
import NoticeBox from '../../../commons/molecules/notice-box/NoticeBox';
import SearchInput, { Message, MessagePropTypesShape } from '../../../commons/molecules/search-input/SearchInput';
import SearchResultListHeader from '../../../commons/molecules/search-result-list-header/SearchResultListHeader';
import SplitViewListSingle from '../split-view-list-single/SplitViewListSingle';
import { ISplitViewListSingle } from '../types/ISplitViewListSingle';
import { ListMode, ListModePropType, ListModeType } from '../types/ListMode';
import { ISearchRequestResult } from '../../../../types/ISearchRequestResult';
import { IBookmarkDict, IBookmarkDictPropType } from '../../../../types/IBookmarkDict';
import { IAppInfoMessages } from '../../../../types/IAppInfoMessages';
import { FilterOption, ISortOrder } from '../../../../types/IContext';
import BookmarkSwitch from '../../../commons/atoms/bookmark-switch/BookmarkSwitch';
import BookmarkListHeader from '../../../commons/molecules/bookmark-list-header/BookmarkListHeader';
import SortButton, { SortButtonState } from '../../../commons/atoms/sort-button/SortButton';
import InformationButton from '../../../commons/atoms/information-button/InformationButton';
import AppInfoContent from '../../app-info/app-info-content/AppInfoContent';
import FilterDropdown from '../../../commons/atoms/filter-dropdown/FilterDropdown';
import type { ISplitViewState, SplitViewDispatch } from '../split-view-container/reducers/SplitViewReducer';
import logger from '../../../../utilities/logger';
import SplitViewListTabs from '../split-view-list-tabs/SplitViewListTabs';
import { SearchListMode, SearchModeType } from '../types/SearchListMode';
import AppAIInfoContent from '../../app-info/app-info-content/AppAIInfoContent';
import { ISpoProperties } from '../../../../types/ISearchResult';
import SettingsButton from '../../../commons/atoms/setting-button/SettingButton';
import { ExtendPopupTimer } from '../../../../hooks/behaviors/useMessageToasterBehavior';
import { ToasterMessage } from '../../../commons/molecules/message-toaster/MessageToaster';
import DataSourceFilterDropdown from '../../../commons/atoms/datasourcefilter-dropdown/DataSourceFilterDropDown';

// CSS
import './SplitViewList.scss';
import type { FilterLeftOptions } from '../../../../utilities/filter/filterSettings';

export const SplitViewListView = {
  LOADING: 'loading',
  DEFAULT: 'default',
  ERROR: 'error',
  ON_INTERVAL: 'onInterval',
  SEARCH_COMPLETED: 'searchCompleted',
};
export type SplitViewListViewType = ValueOf<typeof SplitViewListView>;

export const SplitViewListMessage = {
  BLANK: '',
  INITIAL_DISPLAY: 'INITIAL DISPLAY TEXT',
  API_REQUEST_FAIL: '検索に失敗しました。しばらく時間をおいてから再度アクセスしてください。',
  AI_REQUEST_ERROR: '検索に失敗しました。管理者に確認してください。',
  TOO_MANY_SEARCH_KEYWORDS: '101件以上のキーワードが入力されました。キーワードは100件以下でお試しください。',
  TOO_MANY_SEARCH_CHARACTERS: '256文字以上のキーワードが入力されました。キーワードは255文字以下でお試しください。',
  TOO_MANY_REQUEST: '一度に大量のリクエストが送信されたました。しばらくしてからもう一度お試しください。',
  TOO_MANY_RETRY: '一定時間内にすべての結果を取得できませんでした。再度検索を実施するか画面を再表示すると残りの結果も取得できる場合があります。',
  UNAUTHORIZED: '認証されていないか、取得する権限がありません。管理者に確認してください。',
  UNAVAILABLE: '存在しないリソースのため取得に失敗しました。管理者に確認してください。',
  EMPTY_BOOKMARKS: 'EMPTY BOOKMARKS TEXT',
  NO_BOOKMARKS: 'NO BOOKMARKS TEXT',
  NO_SEARCH_RESULT: 'NO SEARCH RESULT TEXT',
  DISPLAY_LIMIT: 'displayLimit',
};
export type SplitViewListMessageType = ValueOf<typeof SplitViewListMessage>;

export interface ISplitViewListProps {
  className?: string,
  listMode?: ListModeType,
  searchMode?: SearchModeType,
  setSearchMode?: (next: SearchModeType) => void,
  searchRequest?: ISearchRequestResult | null,
  filterOptions: FilterOption[] | undefined,
  emptyFilterOptions?: AsyncState<FilterLeftOptions>,
  view?: SplitViewListViewType,
  message?: SplitViewListMessageType,
  items?: ISplitViewListSingle[],
  activeId?: ISplitViewListSingle['id'],
  bookmarkDict?: IBookmarkDict,
  searchInputValue?: string | null,
  searchInputDisabled?: boolean | null,
  state: ISplitViewState,
  lastSubmittedWords?: string | null,
  sortContexts?: ISortOrder[],
  companyName?: string | null,
  company?: string | null,
  companyRegulationSiteUrl?: string | null,
  isAISearchEnabled?: boolean | null,
  appTitle?: string | null,
  appInfoMessages?: IAppInfoMessages | null,
  dispatch: SplitViewDispatch,
  reportEvent: EventReporter,
  onClickItem?: (item: ISplitViewListSingle) => void,
  onClickBookmark?: (item: ISplitViewListSingle, toBe: boolean) => void,
  extendPopupTimer?: ExtendPopupTimer,
  onClickSort?: () => Promise<void>,
  onSwitchListMode?: (nextMode: ListModeType) => void,
  onSwitchBookmarksList?: () => void,
  onChangeSearchInput?: (value: string) => void,
  onBlurSearchInput?: () => void,
  onSubmitSearchInput?: (value: string) => void,
  onChangeItems?: () => void,
  onClickAppInfo?: () => void,
  onClickShowModal?: () => void,
  onSwitchLastSearchResult?: () => void,
  onCancelSubmit?: () => void,
  replyStateReturn:[reply: string, setReply: (reply: string) => void],
  dateFilterRef: React.MutableRefObject<{from?: string, to?: string}>;
  sourceFilterRef: React.MutableRefObject<string>;
  chatMessages?: Message[],
  onAddChatMessage?: (message: Message) => void,
}
export const SplitViewListLabel = {
  NO_SEARCH_RESULT_HEAD: 'に一致する検索結果が見つかりませんでした',
  NO_SEARCH_RESULT_DESC1: '絞り込み条件を変えるか、キーワードを変えてお試しください。',
  NO_SEARCH_RESULT_DESC2: '中断した場合は、再度実行すると検索結果が取得できる場合があります。',
  NO_BOOKMARKS_HEAD: 'まだお気に入りに追加されたアイテムはありません',
  NO_BOOKMARKS_DESC: '追加するとここに表示されます。',
  EMPTY_BOOKMARKS_FILTER_RESULTS_HEAD: 'お気に入りに表示できるアイテムはありません',
  EMPTY_BOOKMARKS_FILTER_RESULTS_DESC: '絞り込み条件を変えてお試しください。',
  NO_UNREAD_ITEMS_DESC: '未読のお知らせはありません。',
  HINTS_TITLE: '検索のヒント',
  HINTS: [
    '・ SPO記事はタイトル、分類、本文が検索対象となります。添付ファイルは検索対象外となります',
    '・ メールは件名、送受信者、本文、添付ファイルのタイトルが検索対象となります。PDF,Excel,Word,PowerPoint,テキストファイルについては中身の文字も対象となります',
    '・ 迷惑メールフォルダと削除済みフォルダ内のアイテムは検索対象外となります。',
    '・ Teamsはメンション、送受信者、スレッドタイトル、本文、添付ファイルのタイトルが検索対象となります。',
    '・ キーワードが複数入力されている場合、すべてのキーワードを含めた結果のみ表示されます',
    '・ いずれかのキーワードを含むOR検索を行う場合は"OR"をキーワードとキーワードの間に入れて再検索してください',
  ],
  DETAILS: '検索条件についての詳細は',
  HERE: 'こちら',
};

// ローディング時に描画するスケルトンの行数
const LOADING_ROW_COUNT = 20;

// 検索結果の最大表示件数
export const SEARCH_RESULT_MAX_COUNT = 200;

const SplitViewList: React.FC<ISplitViewListProps> = (props) => {
  const {
    className,
    listMode,
    searchMode,
    setSearchMode,
    searchRequest,
    view,
    items,
    activeId,
    bookmarkDict,
    searchInputValue,
    searchInputDisabled,
    message,
    lastSubmittedWords,
    sortContexts,
    filterOptions,
    emptyFilterOptions,
    state,
    companyName,
    company,
    companyRegulationSiteUrl,
    isAISearchEnabled,
    appTitle,
    appInfoMessages,
    dispatch,
    reportEvent,
    onClickItem,
    onClickBookmark,
    extendPopupTimer,
    onClickSort,
    onSwitchListMode,
    onSwitchBookmarksList,
    onChangeSearchInput,
    onBlurSearchInput,
    onSubmitSearchInput,
    onChangeItems,
    onClickAppInfo,
    onClickShowModal,
    onSwitchLastSearchResult,
    onCancelSubmit,
    replyStateReturn,
    dateFilterRef,
    sourceFilterRef,
    chatMessages,
    onAddChatMessage,
  } = props;

  const isDefault = React.useMemo(() => (
    view === SplitViewListView.DEFAULT
  ), [view]);

  const isLoading = React.useMemo(() => (
    view === SplitViewListView.LOADING
  ), [view]);

  const isError = React.useMemo(() => (
    view === SplitViewListView.ERROR
  ), [view]);

  const isInterval = React.useMemo(() => (
    view === SplitViewListView.ON_INTERVAL
  ), [view]);

  const isSearchCompleted = React.useMemo(() => (
    view === SplitViewListView.SEARCH_COMPLETED
  ), [view]);
  // 一覧モードの判定
  const isSearchResultListMode = React.useMemo(() => listMode === ListMode.SEARCH, [listMode]);
  const isBookmarksListMode = React.useMemo(() => listMode === ListMode.BOOKMARKS, [listMode]);
  const isChatSearchMode = React.useMemo(() => searchMode === SearchListMode.Chat, [searchMode]);
  const [reply, setReply] = replyStateReturn;
  // 記事項目ボタンクリックのコールバック
  const handleOnClickItem = React.useCallback((item: ISplitViewListSingle) => {
    if (!onClickItem || activeId === item.id) return;
    onClickItem(item);
  }, [activeId, onClickItem]);

  // お気に入りボタンクリックのコールバック
  const handleOnClickBookmark = React.useCallback((item: ISplitViewListSingle, toBe: boolean) => {
    if (!onClickBookmark) return;

    // 同じeditLinkを持つアイテムが既にお気に入りに登録されているかチェック
    // TODO:idしか保存してない、ので同一のidでフィルターはできない
    // editlinkは持ってないので大幅な修正が必要
    if (toBe && item.kind === 'SPO') {
      const itemProperties = item.properties as ISpoProperties;
      // bookmarkDictから同じeditLinkを持つアイテムを探す
      const sameEditLinkItems = Object.entries(bookmarkDict || {})
        .filter(([_, isBookmarked]) => isBookmarked)
        .map(([bookmarkId]) => items?.find((existingItem) => existingItem.id === bookmarkId))
        .filter((existingItem): existingItem is ISplitViewListSingle => existingItem !== undefined
          && existingItem.kind === 'SPO'
          && (existingItem.properties as ISpoProperties).editLink === itemProperties.editLink);

      if (sameEditLinkItems.length > 0) {
        // 重複エラーをユーザーに通知
        if (extendPopupTimer) {
          extendPopupTimer(ToasterMessage.DUPLICATE_EDIT_LINK);
        }
        return;
      }
    }

    onClickBookmark(item, toBe);
  }, [onClickBookmark, items, bookmarkDict, extendPopupTimer]);

  const update = useUpdate();

  // お気に入り一覧ボタンのクリック
  const onClickBookmarkSwitch = React.useCallback((toBe: boolean) => {
    if (!onSwitchListMode || !onSwitchBookmarksList) return;
    onSwitchListMode(toBe ? ListMode.BOOKMARKS : ListMode.SEARCH);
    if (toBe) {
      // TODO:フィルターリセット、しかし機能してないこと確認、必要か見極める
      onSwitchBookmarksList();
      setReply('');
      // TODO: お気に入り一覧に遷移した時にリストが表示されない問題の暫定対応
      setTimeout(() => {
        update();
      }, 500);
    }
  }, [onSwitchListMode, onSwitchBookmarksList, setReply, update]);
  // Tabボタンクリックのコールバック
  const OnClickTabSwitch = React.useCallback((toBe: boolean) => {
    // AI検索が無効の場合は何もしない
    if (!isAISearchEnabled) return;
    if (!setSearchMode || !onSwitchBookmarksList) return;
    onSwitchBookmarksList();
    // 現在のモードを確認
    const currentMode = searchMode ?? SearchListMode.DEFAULT;
    const targetMode = toBe ? SearchListMode.Chat : SearchListMode.DEFAULT;
    // 現在と同じモードのタブをクリックした場合は何もしない
    if (currentMode === targetMode) return;
    // モードが異なる場合のみ切り替える
    setSearchMode(targetMode);
  }, [setSearchMode, onSwitchBookmarksList, searchMode, isAISearchEnabled]);

  // アプリ情報ボタンクリックのコールバック
  const handleOnClickAppInfo = React.useCallback(() => {
    if (onClickAppInfo) onClickAppInfo();
  }, [onClickAppInfo]);

  // Teams設定ボタン
  const handleOnClickShowModal = React.useCallback(() => {
    if (onClickShowModal)onClickShowModal();
  }, [onClickShowModal]);

  // 社則検索アプリリンクボタンクリック
  const handleOpenCompanyRegulationSite = () => {
    if (companyRegulationSiteUrl) {
      window.open(companyRegulationSiteUrl, '_blank');
    } else {
      logger.error('companyRegulationSiteUrl is not defined');
    }
  };

  // 表示する一覧データ
  const displayItems = React.useMemo(() => {
    if (isLoading || !items) {
      return new Array(LOADING_ROW_COUNT).fill({}).map((v, i) => ({
        id: String(i),
      } as ISplitViewListSingle));
    }
    return items.slice(0, 200);
  }, [isLoading, items]);

  // アクティブ行の判定処理
  const isActiveRow = React.useCallback((itemId?: string) => {
    if (!itemId || !activeId) return false;
    return itemId === activeId;
  }, [activeId]);

  // エラーメッセージ種類の判定
  const isInitialDisplayMessage = React.useMemo(
    () => message === SplitViewListMessage.INITIAL_DISPLAY, [message],
  );
  const isTooManyRetryMessage = React.useMemo(
    () => message === SplitViewListMessage.TOO_MANY_RETRY, [message],
  );
  const isSearchResultSpecialMessage = React.useMemo(
    () => message === SplitViewListMessage.NO_SEARCH_RESULT, [message],
  );
  const isBookmarksSpecialMessage = React.useMemo(
    () => message === SplitViewListMessage.NO_BOOKMARKS, [message],
  );
  const isBookmarksEmptyMessage = React.useMemo(
    () => message === SplitViewListMessage.EMPTY_BOOKMARKS, [message],
  );
  const isGeneralMessage = React.useMemo(
    () => !isBookmarksSpecialMessage && !isSearchResultSpecialMessage && !isInitialDisplayMessage,
    [
      isBookmarksSpecialMessage,
      isSearchResultSpecialMessage,
      isInitialDisplayMessage,
    ],
  );

  const isDisplayLimitMessage = React.useMemo(
    () => message === SplitViewListMessage.DISPLAY_LIMIT, [message],
  );

  const hasTooManyRequests = React.useMemo(
    () => (items ?? []).length > SEARCH_RESULT_MAX_COUNT,
    [items],
  );

  // 検索欄コールバック
  const handleOnChangeSearchInput = React.useCallback((value: string) => {
    if (onChangeSearchInput) onChangeSearchInput(value);
  }, [onChangeSearchInput]);

  const handleOnBlurSearchInput = React.useCallback(() => {
    if (onBlurSearchInput) onBlurSearchInput();
  }, [onBlurSearchInput]);

  const handleOnSubmitSearchInput = React.useCallback((value: string) => {
    if (onSubmitSearchInput) onSubmitSearchInput(value);
  }, [onSubmitSearchInput]);

  // スクロールエリアのクラス名
  const scrollbarClassNames = React.useMemo(() => {
    const step1 = mergedClassName('split-view-list-scroll', isLoading ? 'is-loading' : undefined);
    return mergedClassName(step1, isError ? 'is-error' : undefined);
  }, [isLoading, isError]);

  // お気に入り済判定処理(ループ内部で実行するコールバック)
  const isBookmarked = React.useCallback((id?: string | null) => {
    if (!bookmarkDict || !id) return false;
    return bookmarkDict[id];
  }, [bookmarkDict]);

  // CSSクラス
  const rootClassName = React.useMemo(() => {
    const base = mergedClassName('split-view-list', activeId ? 'is-open' : '');
    const classNames = mergedClassName(base, className ?? '');
    return mergedClassName(classNames, isDefault ? 'is-default' : '');
  }, [activeId, className, isDefault]);

  // 前回の検索結果に切り替える際に実行
  React.useEffect(() => {
    // 検索要求と関数がある場合
    // submittedValueを更新して、モードを切り替える
    if (searchRequest && onSwitchLastSearchResult) onSwitchLastSearchResult();

  }, [
    searchRequest,
    onSwitchLastSearchResult,
  ]);

  // ハイライト機能
  React.useEffect(() => {
    // ハイライトするのは検索が完了した時のみ
    if (isSearchCompleted && onChangeItems) {
      onChangeItems();
    }
  }, [items, onChangeItems, isSearchCompleted]);
  // stateの持つ数字によって選択肢が変わる
  const displayDateOption = filterOptions?.find(({ key }) => key === 'displayDate')?.option;
  const kindOption = filterOptions?.find(({ key }) => key === 'kind')?.option;
  const FeaturesFlag = FeaturesFlags.find(
    (flag) => flag.company === company,
  ) || FeaturesFlags.find(
    (flag) => flag.company === 'default',
  );
  const renderCancellationButton = () => {
    if (searchMode === SearchListMode.DEFAULT) {
      return <CancellationButton onClick={onCancelSubmit} />;
    }
    // チャットモードの場合は中断ボタンを表示しない
    if (searchMode === SearchListMode.Chat) {
      return null;
    }
    return null;
  };
  // TODO:Mailアイテムはフォルダが変わるとidが変わるのでIndexで対応必要
  return (
    <div className={rootClassName}>
      <div className="split-view-list-title">
        {/* this is the companyName */}
        <div className="split-view-title-company-row">
          <Text className="split-view-title-company" as="h3" content={companyName} truncated />
          {/* this is the main list head */}
          {
            (FeaturesFlag && FeaturesFlag.isCompanyRegulationSite) && (
              <div className="split-view-list-companyRegulationSite-info">
                <Text content="社則検索は" />
                <InformationButton onClick={handleOpenCompanyRegulationSite} />
              </div>
            )
          }
        </div>
        <div className="split-view-title-main-row">
          <Text className="split-view-title-main" as="h2" content={appTitle} truncated />
          <div className="split-view-list-app-info">
            <Text content="検索対象・使い方は" />
            <InformationButton onClick={handleOnClickAppInfo} />
          </div>
        </div>
      </div>
      <div className="split-view-list-card">
        <div className="split-view-list-header-container">
          <div className="split-view-list-header">
            {isAISearchEnabled && (
            <div className="split-view-list-header-tab">
              {/* SplitViewListTabsはAI検索が有効な場合のみ表示 */}
              <SplitViewListTabs
                mode={searchMode ?? SearchListMode.DEFAULT}
                isActive={isChatSearchMode}
                onClick={OnClickTabSwitch}
                disabled={isLoading}
                reportEvent={reportEvent}
                isAISearchEnabled={isAISearchEnabled}
              />
              {/* SettingsButtonは条件を満たす場合のみ表示（お気に入りモードでは非表示） */}
              {(isChatSearchMode && !isBookmarksListMode && isSearchCompleted) && (
              <SettingsButton
                onClick={handleOnClickShowModal}
                isActive={false}
                className="settings-btn"
              />
              )}
            </div>
            )}
            {/* チャット検索モードのときはフィルター類を上に */}
            {isChatSearchMode && (
              (isBookmarksListMode
                || (isSearchCompleted)
              )
            ) && (
              <div className="split-view-list-header-row filter-and-sorter">
                <SortButton
                  className="split-view-list-sort-button"
                  searchMode={searchMode}
                  listMode={listMode}
                  state={sortContexts?.find((condition) => {
                    let sortKey = 'displayDate';
                    if (listMode !== ListMode.BOOKMARKS && searchMode === 'Chat') {
                      sortKey = 'score';
                    }
                    return condition.key === sortKey;
                  })?.order ?? SortButtonState.DEFAULT}
                  onClick={onClickSort}
                />
                <FilterDropdown
                  selectedKey="displayDate"
                  selectedOption={displayDateOption as number}
                  state={state}
                  listMode={listMode}
                  dispatch={dispatch}
                  reportEvent={reportEvent}
                  resultEmptyList={emptyFilterOptions}
                  dateFilterRef={dateFilterRef}
                  searchMode={searchMode}
                />
                <DataSourceFilterDropdown
                  selectedKey="kind"
                  selectedOption={kindOption as string[]}
                  listMode={listMode}
                  state={state}
                  dispatch={dispatch}
                  reportEvent={reportEvent}
                  resultEmptyList={emptyFilterOptions}
                  sourceFilterRef={sourceFilterRef}
                  searchMode={searchMode}
                />
              </div>
            )}
            {/* 検索入力とお気に入り */}
            <div className="split-view-list-header-row">
              <SearchInput
                className="split-view-list-search-bar"
                value={searchInputValue}
                onChange={handleOnChangeSearchInput}
                onBlur={handleOnBlurSearchInput}
                onSubmit={handleOnSubmitSearchInput}
                disabled={searchInputDisabled ?? false}
                searchMode={searchMode}
                reply={reply}
                messages={chatMessages}
                onAddMessage={onAddChatMessage}
              />
              <BookmarkSwitch
                className="split-view-list-header-switch"
                isActive={isBookmarksListMode}
                onClick={onClickBookmarkSwitch}
              />
            </div>
            {
              (hasTooManyRequests && isSearchCompleted) && (
                <div className="too-many-search-results-message">
                  <Text>検索結果が多すぎるため、一部のみ表示しています</Text>
                </div>
              )
            }
            {/* 並び替えボタン */}
            {!isChatSearchMode && (
              (isBookmarksListMode
                || (isSearchCompleted
              // chatModeの初期画面で表示させたいのでコメントアウト
              // && !isInitialDisplayMessage
                )
              ) && (
              <div className="split-view-list-header-row filter-and-sorter">
                <SortButton
                  className="split-view-list-sort-button"
                  searchMode={searchMode}
                  listMode={listMode}
                  state={sortContexts?.find((condition) => {
                    let sortKey = 'displayDate';
                    if (listMode !== ListMode.BOOKMARKS && searchMode === 'Chat') {
                      sortKey = 'score';
                    }
                    return condition.key === sortKey;
                  })?.order ?? SortButtonState.DEFAULT}
                  onClick={onClickSort}
                />
                <FilterDropdown
                    // IContex内のdisplayDateを取得
                  selectedKey="displayDate"
                    // 選択肢の数字を持っている
                  selectedOption={displayDateOption as number}
                  state={state}
                  listMode={listMode}
                  dispatch={dispatch}
                  reportEvent={reportEvent}
                    // 例えば10件のうちoption1は2件、option2は3件と言うようにそれぞれ何個ずつ該当するものがあるか持っている
                  resultEmptyList={emptyFilterOptions}
                    // フィルター変更するstate
                  dateFilterRef={dateFilterRef}
                  searchMode={searchMode}
                />
                <DataSourceFilterDropdown
                  selectedKey="kind"
                  selectedOption={kindOption as string[]}
                  listMode={listMode}
                  state={state}
                  dispatch={dispatch}
                  reportEvent={reportEvent}
                  resultEmptyList={emptyFilterOptions}
                  sourceFilterRef={sourceFilterRef}
                  searchMode={searchMode}
                />
              </div>
              ))}
            {
              (isSearchResultListMode
                && !isSearchCompleted
                && !isSearchResultSpecialMessage
                && !isError
              )
              && (
                <div className="split-view-list-header-loader">
                  <Loader size="smallest" inline />
                  <Text>検索結果取得中・・・</Text>
                  {renderCancellationButton()}
                </div>
              )
            }
          </div>

          {/* 検索結果一覧の件数表示 */}
          {isSearchResultListMode && (isInterval || isSearchCompleted) && (
            <SearchResultListHeader
              className="split-view-list-header-search-result-head"
              count={items?.length}
              maxCount={SEARCH_RESULT_MAX_COUNT}
              words={lastSubmittedWords}
              searchMode={searchMode}
            />
          )}

          {/* お気に入り一覧の件数表示 */}
          {isBookmarksListMode && (
            <BookmarkListHeader className="split-view-list-header-bookmarks-head" count={items?.length ?? 0} />
          )}
        </div>

        <Scrollbars className={scrollbarClassNames} autoHide>
          <div className="split-view-list-content">
            {isError && isGeneralMessage && (
              <div data-testid="message" className="split-view-list-message-container">
                {/* 通常エラー */}
                <NoticeBox message={message} className="split-view-list-message" />
              </div>
            )}
            {/* お気に入り0件時 */}
            {
              isBookmarksListMode
              && items?.length === 0
              && (
                <div data-testid="message" className="split-view-list-message-container">
                  <div className="notice-box split-view-list-message split-view-list-message-no-bookmarks">
                    <Text className="notice-box-text">
                      {
                        isBookmarksSpecialMessage
                        && SplitViewListLabel.NO_BOOKMARKS_HEAD
                      }
                      {
                        isBookmarksEmptyMessage
                        && SplitViewListLabel.EMPTY_BOOKMARKS_FILTER_RESULTS_HEAD
                      }
                    </Text>
                    <br />
                    <Text>
                      {
                        isBookmarksSpecialMessage
                        && SplitViewListLabel.NO_BOOKMARKS_DESC
                      }
                      {
                        isBookmarksEmptyMessage
                        && SplitViewListLabel.EMPTY_BOOKMARKS_FILTER_RESULTS_DESC
                      }
                    </Text>
                  </div>
                </div>
              )
            }
            {/* 検索未実施時のデフォルト表示 */}
            {isSearchCompleted && isInitialDisplayMessage && (
            <div data-testid="message" className="split-view-list-message-container">
              <div className="split-view-list-app-info-messages">
                {searchMode === SearchListMode.Chat
                  ? (
                    <AppAIInfoContent
                      displayMessages={appInfoMessages ?? {}}
                    />
                  )
                  : (
                    <AppInfoContent
                      displayMessages={appInfoMessages ?? {}}
                    />
                  )}
              </div>
            </div>
            )}
            {/* 検索結果0件時 */}
            {
              (
                isSearchResultListMode
                && isSearchCompleted
                && (
                  items?.length === 0
                  && !isInitialDisplayMessage
                  && !isTooManyRetryMessage
                )
              ) && (
                <div className="split-view-list-message split-view-list-message-no-searchresult">
                  <Text className="notice-box-text">
                    {lastSubmittedWords?.[0] && `「${lastSubmittedWords}」`}
                    {SplitViewListLabel.NO_SEARCH_RESULT_HEAD}
                  </Text>
                  <div className="split-view-list-message-no-searchresult-desc">
                    <Text content={SplitViewListLabel.NO_SEARCH_RESULT_DESC1} />
                    <Text content={SplitViewListLabel.NO_SEARCH_RESULT_DESC2} />
                    <br />
                    <Text content={`再${SplitViewListLabel.HINTS_TITLE}`} />
                    {SplitViewListLabel.HINTS.map((hint) => <Text key={hint} content={hint} />)}
                    <span className="information-message">
                      <Text content={SplitViewListLabel.DETAILS} />
                      <Button
                        className="information-button here"
                        content={SplitViewListLabel.HERE}
                        onClick={onClickAppInfo}
                        size="small"
                        text
                      />
                    </span>
                  </div>
                </div>
              )
            }
            {!isError && !isInitialDisplayMessage && (
              <ul className="split-view-list-list">
                {displayItems.map((item) => (
                  <li key={item.id} className="split-view-list-item">
                    <SplitViewListSingle
                      className={isActiveRow(item.id ?? '') ? 'is-active' : undefined}
                      view={view}
                      item={isLoading ? undefined : item}
                      isBookmarked={isBookmarked(item.id)}
                      onClick={handleOnClickItem}
                      onClickBookmark={handleOnClickBookmark}
                      // chatMode={isChatSearchMode}
                    />
                  </li>
                ))}
                {(isInterval && !isDisplayLimitMessage) && (
                  <li key="loading-icon">
                    <br />
                    <Loader />
                  </li>
                )}
                {isSearchCompleted && isTooManyRetryMessage && (
                  <li key="too-many-retry-message" className="split-view-list-message split-view-list-message-too-many-retry">
                    {
                      splitWithSeparator(SplitViewListMessage.TOO_MANY_RETRY, '。').map((messageFragment) => (
                        <Text className="split-view-list-message-no-searchresult-desc" content={messageFragment} />
                      ))
                    }
                  </li>
                )}
              </ul>
            )}
          </div>
        </Scrollbars>
      </div>
    </div>
  );
};

SplitViewList.propTypes = {
  className: PropTypes.string,
  listMode: ListModePropType,
  view: PropTypes.string,
  message: PropTypes.string,
  activeId: PropTypes.string,
  bookmarkDict: IBookmarkDictPropType,
  searchInputValue: PropTypes.string,
  searchInputDisabled: PropTypes.bool,
  lastSubmittedWords: PropTypes.string,
  onClickItem: PropTypes.func,
  onClickBookmark: PropTypes.func,
  extendPopupTimer: PropTypes.func,
  onSwitchBookmarksList: PropTypes.func,
  onSwitchListMode: PropTypes.func,
  onChangeSearchInput: PropTypes.func,
  onBlurSearchInput: PropTypes.func,
  onSubmitSearchInput: PropTypes.func,
  onChangeItems: PropTypes.func,
  onClickAppInfo: PropTypes.func,
  onClickShowModal: PropTypes.func,
  onSwitchLastSearchResult: PropTypes.func,
  onCancelSubmit: PropTypes.func,
  chatMessages: PropTypes.arrayOf(PropTypes.shape(MessagePropTypesShape)) as PropTypes.Validator<
    Message[] | null | undefined
  >,
  onAddChatMessage: PropTypes.func,
};

SplitViewList.defaultProps = {
  className: undefined,
  listMode: ListMode.INITIAL_DISPLAY,
  view: SplitViewListView.LOADING,
  message: SplitViewListMessage.BLANK,
  items: [],
  activeId: '',
  bookmarkDict: {},
  searchInputValue: '',
  searchInputDisabled: false,
  lastSubmittedWords: '',
  sortContexts: [],
  companyName: null,
  company: null,
  companyRegulationSiteUrl: null,
  isAISearchEnabled: null,
  appTitle: null,
  appInfoMessages: null,
  searchRequest: null,
  emptyFilterOptions: undefined,
  searchMode: SearchListMode.DEFAULT,
  setSearchMode: undefined,
  onClickItem: undefined,
  onClickBookmark: undefined,
  extendPopupTimer: undefined,
  onClickSort: undefined,
  onSwitchBookmarksList: undefined,
  onSwitchListMode: undefined,
  onChangeSearchInput: undefined,
  onBlurSearchInput: undefined,
  onSubmitSearchInput: undefined,
  onChangeItems: undefined,
  onClickAppInfo: undefined,
  onClickShowModal: undefined,
  onSwitchLastSearchResult: undefined,
  onCancelSubmit: undefined,
  chatMessages: undefined,
  onAddChatMessage: undefined,
};

export default React.memo(SplitViewList);
