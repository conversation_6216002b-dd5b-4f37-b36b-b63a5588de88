import * as React from 'react';
import { EventReportType, EventReporter } from '@avanade-teams/app-insights-reporter';
import {
  Dropdown, DropdownItemProps, DropdownProps,
} from '@fluentui/react-northstar';
import { AsyncState } from 'react-use/lib/useAsync';
import type { ISplitViewState, SplitViewDispatch } from '../../../domains/split-view/split-view-container/reducers/SplitViewReducer';
import { ListMode, ListModeType } from '../../../domains/split-view/types/ListMode';
import { mergedClassName } from '../../../../utilities/commonFunction';

import './DataSourceFilterDropDown.scss';
import useFilterOptionBehavior from '../../../../hooks/behaviors/useFilterOptionBehavior';
import modifyDisplayDateOption from '../../../domains/utilities/logging/modifyDisplayDateOption';
import DataSourceIcon from '../datasource-icon/DataSourceIcon';
import { FilterOption } from '../../../../types/IContext';
import { FilterLeftOptions, MaxFilterDisplayCount } from '../../../../utilities/filter/filterSettings';
import { SearchModeType } from '../../../domains/split-view/types/SearchListMode';

export interface IFilterDropdownProps {
  selectedKey: 'kind' | 'displayDate';
  className?: string;
  selectedOption: string[] | undefined;
  state: ISplitViewState;
  listMode?: ListModeType,
  dispatch: SplitViewDispatch;
  reportEvent: EventReporter,
  resultEmptyList?: AsyncState<FilterLeftOptions>,
  sourceFilterRef: React.MutableRefObject<string>;
  searchMode?: SearchModeType;
}

export const FilterOptions = [
  { header: 'SPO', variables: { key: 'SPO', index: 0 } },
  { header: 'Outlook', variables: { key: 'Mail', index: 1 } },
  { header: 'Teams', variables: { key: 'Chat', index: 2 } },
];

export function calculateResultCount(
  selectedValues: { variables?: any }[],
  resultEmptyList: number[],
) {
  if (selectedValues.length > 0) {
    // indexが取れなかったときは0を足すようにする
    return selectedValues.map(
      (item) => (item.variables?.index === undefined ? 0 : resultEmptyList[item.variables.index]),
    ).reduce(
      (a, b) => a + b,
    );
  }
  return resultEmptyList.reduce((a, b) => a + b);
}

export function onSelectionChanged(
  data: DropdownProps,
  selectedKey: 'kind' | 'displayDate',
  filter: FilterOption[],
  resultEmptyList: number[] | undefined,
  listMode: string | undefined,
  onSelectOption: (option: FilterOption) => void,
  reportEvent: EventReporter,
  searchMode?: SearchModeType,
) {

  const selectedValues = (data.value as DropdownItemProps[]);
  const option = selectedValues.map(({ variables }) => variables.key);
  if (selectedKey === 'kind') {
    onSelectOption({
      key: selectedKey,
      option: option as string[],
    });
  } else if (selectedKey === 'displayDate') {
    onSelectOption({
      key: selectedKey,
      option: parseInt(option[0], 10),
    });
  }

  const resultCount = calculateResultCount(selectedValues, resultEmptyList ?? []);
  const filterConditions: FilterOption = selectedKey === 'kind'
    ? { key: 'kind', option: option as string[] }
    : { key: 'displayDate', option: parseInt(option[0], 10) };

  const changedFilter = [
    ...filter.filter(({ key }) => key !== selectedKey),
    filterConditions,
  ];
  let eventName: string;
  // AI検索かデフォルト検索でログを分割
  if (listMode === ListMode.SEARCH) {
    eventName = searchMode === 'Chat'
      ? 'EXECUTE_AI_SEARCH_LIST_FILTER'
      : 'EXECUTE_DEFAULT_SEARCH_LIST_FILTER';
  } else {
    eventName = 'EXECUTE_BOOKMARK_LIST_FILTER';
  }
  reportEvent({
    type: EventReportType.USER_EVENT,
    name: eventName,
    customProperties: {
      filterAction: selectedKey,
      filterOptionName: option,
      filterConditions: modifyDisplayDateOption(changedFilter),
      resultCount,
    },
  });
}

const DataSourceFilterDropdown: React.FC<IFilterDropdownProps> = (props) => {
  const {
    selectedKey,
    selectedOption,
    state,
    listMode,
    dispatch,
    reportEvent,
    className,
    resultEmptyList,
    sourceFilterRef,
    searchMode,
  } = props;
  // searchModeが変更された時にsourceFilterRefを初期化
  React.useEffect(() => {
    sourceFilterRef.current = '';
  }, [searchMode, sourceFilterRef]);

  const rootClassName = React.useMemo(() => mergedClassName('datasource-filter-dropdown', className), [className]);
  const filterOptionBehavior = useFilterOptionBehavior(state, dispatch);

  const hasResults = Array.isArray(state.list) && state.list.length > 0;
  const items = FilterOptions.map((value, index) => {
    const rawCount = hasResults
      ? ((resultEmptyList?.value ?? {
        displayDate: [],
        kind: [],
      })[selectedKey][value.variables.index] ?? 0)
      : 0;
    // eslint-disable-next-line no-nested-ternary
    const countDisplay = resultEmptyList?.loading === true
      ? '計算中'
      : rawCount > MaxFilterDisplayCount
        ? `${rawCount - 1}+`
        : rawCount;

    return {
      ...value,
      variables: {
        ...value.variables,
        // 検索前は空文字にして表示しない
        count: hasResults ? countDisplay : '',
      },
      className: resultEmptyList?.value?.[selectedKey]?.[index] === 0 ? 'result-empty' : '',
    };
  });
  // 渡ってきたselectedOptionを画面表示している
  const values = FilterOptions.filter(
    ({ variables: v }) => (selectedOption ?? []).some((k) => v.key === k),
  );

  const handleSelectionChanged = React.useCallback((data: DropdownProps) => {
    const selectedItems = data.value as DropdownItemProps[];
    const keys = selectedItems.map((i) => i.variables.key as string);
    // フィルターをセット
    sourceFilterRef.current = keys.join(',');
    onSelectionChanged(
      data,
      selectedKey,
      state.context.filter ?? [],
      resultEmptyList?.value?.[selectedKey],
      listMode,
      filterOptionBehavior.onSelectOption,
      reportEvent,
      searchMode,
    );
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    selectedKey,
    listMode,
    state.context.filter,
    resultEmptyList,
    filterOptionBehavior,
    reportEvent,
    searchMode,
  ]);
  return (
    <Dropdown
      className={rootClassName}
      onChange={(_, data) => handleSelectionChanged(data)}
      // eslint-disable-next-line react/jsx-props-no-spreading
      renderItem={(Item, p) => (
        <Item
          // eslint-disable-next-line react/jsx-props-no-spreading
          {...p}
          header={(
            <>
              <DataSourceIcon kind={p.variables.key} />
              <span>
                {p.header}
                {/** 検索結果があれば数字を表示する */}
                {hasResults && (
                  (
                  <>
                    {' '}
                    (
                    {p.variables.count}
                    )
                  </>
                  )
                )}
              </span>
            </>
          )}
          className={mergedClassName('filter-item', p.className)}
        />
      )}
      // eslint-disable-next-line react/jsx-props-no-spreading
      renderSelectedItem={(Item, p) => (<Item {...p} header={<DataSourceIcon kind={p.variables.key} />} className={mergedClassName('filter-item selected', p.className)} />)}
      multiple
      value={values}
      items={items}
      placeholder="検索対象を選択"
      noResultsMessage="他の検索対象はありません"
    />
  );
};

DataSourceFilterDropdown.defaultProps = {
  className: '',
  listMode: undefined,
  resultEmptyList: undefined,
  searchMode: undefined,
};

export default DataSourceFilterDropdown;
